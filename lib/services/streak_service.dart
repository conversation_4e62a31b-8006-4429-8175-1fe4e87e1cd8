import 'dart:async';
import 'package:unstack/models/enhanced_task.dart';
import 'package:unstack/models/enhanced_streak_data.dart';
import 'package:unstack/repositories/task_repository.dart';
import 'package:unstack/repositories/streak_repository.dart';
import 'package:unstack/utils/app_logger.dart';

/// Service for managing streak calculations and real-time updates
class StreakService {
  final TaskRepository taskRepository;
  final StreakRepository _streakRepository;

  // Stream controllers for real-time updates
  final StreamController<StreakStatistics> _streakStatsController =
      StreamController<StreakStatistics>.broadcast();
  final StreamController<List<EnhancedTask>> _todaysTasksController =
      StreamController<List<EnhancedTask>>.broadcast();
  final StreamController<bool> _streakValidityController =
      StreamController<bool>.broadcast();

  StreakService({
    TaskRepository? taskRepository,
    StreakRepository? streakRepository,
  })  : taskRepository = taskRepository ?? TaskRepository(),
        _streakRepository = streakRepository ?? StreakRepository();

  // Stream getters for UI to listen to
  Stream<StreakStatistics> get streakStatsStream =>
      _streakStatsController.stream;
  Stream<List<EnhancedTask>> get todaysTasksStream =>
      _todaysTasksController.stream;
  Stream<bool> get streakValidityStream => _streakValidityController.stream;

  /// Initialize the service and load initial data
  Future<void> initialize() async {
    try {
      AppLogger.info('Initializing StreakService...');
      await _updateTodaysStreak();
      await _broadcastCurrentState();
      AppLogger.info('StreakService initialized successfully');
    } catch (e) {
      AppLogger.error('Failed to initialize StreakService: $e');
      rethrow;
    }
  }

  /// Add a new task and update streak if necessary
  Future<EnhancedTask> addTask({
    required String title,
    required String description,
    required TaskPriority priority,
    required DateTime assignedDate,
    List<String> tags = const [],
  }) async {
    try {
      final now = DateTime.now();
      final displayOrder = await taskRepository.getNextDisplayOrder();

      final task = EnhancedTask(
        id: _generateTaskId(),
        title: title,
        description: description,
        priority: priority,
        createdAt: now,
        assignedDate: assignedDate,
        displayOrder: displayOrder,
        tags: tags,
        updatedAt: now,
      );

      await taskRepository.createTask(task);
      AppLogger.info('Task added: ${task.id}');

      // Update streak if task is assigned to today
      if (task.isAssignedToday) {
        await _updateTodaysStreak();
        await _broadcastCurrentState();
      }

      return task;
    } catch (e) {
      AppLogger.error('Failed to add task: $e');
      rethrow;
    }
  }

  /// Toggle task completion and update streak
  Future<bool> toggleTaskCompletion(String taskId, bool isCompleted) async {
    try {
      final task = await taskRepository.getTaskById(taskId);
      if (task == null) {
        AppLogger.warning('Task not found: $taskId');
        return false;
      }

      final success =
          await taskRepository.toggleTaskCompletion(taskId, isCompleted);
      if (success) {
        AppLogger.info('Task completion toggled: $taskId -> $isCompleted');

        // Update streak if task is assigned to today
        if (task.isAssignedToday) {
          await _updateTodaysStreak();
          await _broadcastCurrentState();
        }
      }

      return success;
    } catch (e) {
      AppLogger.error('Failed to toggle task completion: $e');
      rethrow;
    }
  }

  /// Delete a task and update streak if necessary
  Future<bool> deleteTask(String taskId) async {
    try {
      final task = await taskRepository.getTaskById(taskId);
      if (task == null) {
        AppLogger.warning('Task not found: $taskId');
        return false;
      }

      final wasAssignedToday = task.isAssignedToday;
      final success = await taskRepository.deleteTask(taskId);

      if (success) {
        AppLogger.info('Task deleted: $taskId');

        // Update streak if task was assigned to today
        if (wasAssignedToday) {
          await _updateTodaysStreak();
          await _broadcastCurrentState();
        }
      }

      return success;
    } catch (e) {
      AppLogger.error('Failed to delete task: $e');
      rethrow;
    }
  }

  /// Move task to a different date and update streaks
  Future<bool> moveTaskToDate(String taskId, DateTime newDate) async {
    try {
      final task = await taskRepository.getTaskById(taskId);
      if (task == null) {
        AppLogger.warning('Task not found: $taskId');
        return false;
      }

      final wasAssignedToday = task.isAssignedToday;
      final success = await taskRepository.moveTaskToDate(taskId, newDate);

      if (success) {
        AppLogger.info('Task moved to date: $taskId -> $newDate');

        // Update today's streak if task was moved from or to today
        final updatedTask = await taskRepository.getTaskById(taskId);
        if (wasAssignedToday || (updatedTask?.isAssignedToday ?? false)) {
          await _updateTodaysStreak();
          await _broadcastCurrentState();
        }
      }

      return success;
    } catch (e) {
      AppLogger.error('Failed to move task to date: $e');
      rethrow;
    }
  }

  /// Reorder tasks (for drag-and-drop)
  Future<bool> reorderTasks(List<String> taskIds) async {
    try {
      final success = await taskRepository.reorderTasks(taskIds);
      if (success) {
        AppLogger.info('Tasks reordered');
        await _broadcastCurrentState();
      }
      return success;
    } catch (e) {
      AppLogger.error('Failed to reorder tasks: $e');
      rethrow;
    }
  }

  /// Get today's tasks
  Future<List<EnhancedTask>> getTodaysTasks() async {
    try {
      return await taskRepository.getTodaysTasks();
    } catch (e) {
      AppLogger.error('Failed to get today\'s tasks: $e');
      rethrow;
    }
  }

  /// Get tomorrow's tasks
  Future<List<EnhancedTask>> getTomorrowsTasks() async {
    try {
      return await taskRepository.getTomorrowsTasks();
    } catch (e) {
      AppLogger.error('Failed to get tomorrow\'s tasks: $e');
      rethrow;
    }
  }

  /// Get current streak statistics
  Future<StreakStatistics> getStreakStatistics() async {
    try {
      return await _streakRepository.getStreakStatistics();
    } catch (e) {
      AppLogger.error('Failed to get streak statistics: $e');
      return StreakStatistics.empty();
    }
  }

  /// Get streak data for a specific month
  Future<List<EnhancedStreakData>> getMonthlyStreakData(DateTime month) async {
    try {
      final startOfMonth = DateTime(month.year, month.month, 1);
      final endOfMonth = DateTime(month.year, month.month + 1, 0);

      return await _streakRepository.getStreakDataInRange(
          startOfMonth, endOfMonth);
    } catch (e) {
      AppLogger.error('Failed to get monthly streak data: $e');
      return [];
    }
  }

  /// Check if today's streak is valid
  Future<bool> isTodaysStreakValid() async {
    try {
      final today = DateTime.now();
      final streakData = await _streakRepository.getStreakDataByDate(today);
      return streakData?.streakValid ?? false;
    } catch (e) {
      AppLogger.error('Failed to check today\'s streak validity: $e');
      return false;
    }
  }

  /// Force recalculate all streak data (for data consistency)
  Future<void> recalculateAllStreaks() async {
    try {
      AppLogger.info('Recalculating all streak data...');

      // Get all tasks grouped by assigned date
      final allTasks = await taskRepository.getAllTasks();
      final tasksByDate = <DateTime, List<EnhancedTask>>{};

      for (final task in allTasks) {
        final dateKey = DateTime(task.assignedDate.year,
            task.assignedDate.month, task.assignedDate.day);
        tasksByDate.putIfAbsent(dateKey, () => []).add(task);
      }

      // Recalculate streak data for each date
      for (final entry in tasksByDate.entries) {
        await _streakRepository.calculateAndUpdateStreakData(
            entry.key, entry.value);
      }

      await _broadcastCurrentState();
      AppLogger.info('All streak data recalculated successfully');
    } catch (e) {
      AppLogger.error('Failed to recalculate all streaks: $e');
      rethrow;
    }
  }

  /// Update today's streak data
  Future<void> _updateTodaysStreak() async {
    try {
      final today = DateTime.now();
      final todaysTasks = await taskRepository.getTodaysTasks();
      await _streakRepository.calculateAndUpdateStreakData(today, todaysTasks);
    } catch (e) {
      AppLogger.error('Failed to update today\'s streak: $e');
      rethrow;
    }
  }

  /// Broadcast current state to all listeners
  Future<void> _broadcastCurrentState() async {
    try {
      final streakStats = await getStreakStatistics();
      final todaysTasks = await getTodaysTasks();
      final isValid = await isTodaysStreakValid();

      _streakStatsController.add(streakStats);
      _todaysTasksController.add(todaysTasks);
      _streakValidityController.add(isValid);
    } catch (e) {
      AppLogger.error('Failed to broadcast current state: $e');
    }
  }

  /// Generate unique task ID
  String _generateTaskId() {
    return 'task_${DateTime.now().millisecondsSinceEpoch}_${DateTime.now().microsecond}';
  }

  /// Dispose of stream controllers
  void dispose() {
    _streakStatsController.close();
    _todaysTasksController.close();
    _streakValidityController.close();
    AppLogger.info('StreakService disposed');
  }
}
