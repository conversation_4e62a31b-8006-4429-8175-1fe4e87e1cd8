import 'package:device_preview/device_preview.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unstack/firebase_options.dart';
import 'package:unstack/models/enhanced_task.dart';
import 'package:unstack/providers/task_provider.dart';
import 'package:unstack/theme/app_theme.dart';
import 'package:unstack/utils/app_logger.dart';
import 'package:unstack/views/enhanced_home_page.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    AppLogger.info('Firebase initialized successfully');
  } catch (e) {
    AppLogger.error('Firebase initialization failed: $e');
  }

  runApp(
    DevicePreview(
      enabled: kDebugMode,
      builder: (context) => const TestEnhancedApp(),
    ),
  );
}

class TestEnhancedApp extends StatelessWidget {
  const TestEnhancedApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) => TaskProvider(),
          lazy: false, // Initialize immediately
        ),
      ],
      child: MaterialApp(
        title: 'Unstack - Enhanced Test',
        debugShowCheckedModeBanner: false,
        debugShowMaterialGrid: false,
        theme: AppTheme.darkTheme,
        home: const TestHomePage(),
        builder: DevicePreview.appBuilder,
      ),
    );
  }
}

class TestHomePage extends StatelessWidget {
  const TestHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      appBar: AppBar(
        title: const Text('Enhanced Streak System Test'),
        backgroundColor: AppColors.backgroundPrimary,
        elevation: 0,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Test Enhanced Home Page
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const EnhancedHomePage(),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.accentPurple,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
              ),
              child: const Text(
                'Test Enhanced Home Page',
                style: TextStyle(color: Colors.white),
              ),
            ),

            const SizedBox(height: 20),

            // Test Database Connection
            Consumer<TaskProvider>(
              builder: (context, taskProvider, child) {
                return Column(
                  children: [
                    ElevatedButton(
                      onPressed: () async {
                        await taskProvider.addTask(
                          title: 'Test Task ${DateTime.now().millisecond}',
                          description: 'This is a test task',
                          priority: TaskPriority.medium,
                          assignedDate: DateTime.now(),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.accentGreen,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 32,
                          vertical: 16,
                        ),
                      ),
                      child: const Text(
                        'Add Test Task',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                    const SizedBox(height: 10),
                    Text(
                      'Total Tasks: ${taskProvider.totalTasksCount}',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textPrimary,
                      ),
                    ),
                    Text(
                      'Today\'s Tasks: ${taskProvider.todaysTasksCount}',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textPrimary,
                      ),
                    ),
                    Text(
                      'Current Streak: ${taskProvider.streakStatistics.currentStreak}',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textPrimary,
                      ),
                    ),
                    if (taskProvider.errorMessage != null)
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          'Error: ${taskProvider.errorMessage}',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.statusError,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
