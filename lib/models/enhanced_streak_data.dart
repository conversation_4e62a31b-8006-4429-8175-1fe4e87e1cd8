/// Enhanced streak data model with database support
class EnhancedStreakData {
  final int? id;
  final DateTime date;
  final int totalTasks;
  final int completedTasks;
  final bool allTasksCompleted;
  final bool streakValid;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EnhancedStreakData({
    this.id,
    required this.date,
    required this.totalTasks,
    required this.completedTasks,
    required this.allTasksCompleted,
    required this.streakValid,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create from database map
  factory EnhancedStreakData.fromMap(Map<String, dynamic> map) {
    return EnhancedStreakData(
      id: map['id'] as int?,
      date: DateTime.fromMillisecondsSinceEpoch((map['date'] as int) * 1000),
      totalTasks: map['total_tasks'] as int,
      completedTasks: map['completed_tasks'] as int,
      allTasksCompleted: (map['all_tasks_completed'] as int) == 1,
      streakValid: (map['streak_valid'] as int) == 1,
      createdAt: DateTime.fromMillisecondsSinceEpoch((map['created_at'] as int) * 1000),
      updatedAt: DateTime.fromMillisecondsSinceEpoch((map['updated_at'] as int) * 1000),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'date': _getStartOfDay(date).millisecondsSinceEpoch ~/ 1000,
      'total_tasks': totalTasks,
      'completed_tasks': completedTasks,
      'all_tasks_completed': allTasksCompleted ? 1 : 0,
      'streak_valid': streakValid ? 1 : 0,
      'created_at': createdAt.millisecondsSinceEpoch ~/ 1000,
      'updated_at': updatedAt.millisecondsSinceEpoch ~/ 1000,
    };
  }

  /// Create a copy with updated fields
  EnhancedStreakData copyWith({
    int? id,
    DateTime? date,
    int? totalTasks,
    int? completedTasks,
    bool? allTasksCompleted,
    bool? streakValid,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EnhancedStreakData(
      id: id ?? this.id,
      date: date ?? this.date,
      totalTasks: totalTasks ?? this.totalTasks,
      completedTasks: completedTasks ?? this.completedTasks,
      allTasksCompleted: allTasksCompleted ?? this.allTasksCompleted,
      streakValid: streakValid ?? this.streakValid,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Get completion percentage
  double get completionPercentage {
    if (totalTasks == 0) return 0.0;
    return completedTasks / totalTasks;
  }

  /// Check if this is today's data
  bool get isToday {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dataDate = DateTime(date.year, date.month, date.day);
    return dataDate.isAtSameMomentAs(today);
  }

  /// Get start of day for consistent date comparison
  static DateTime _getStartOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EnhancedStreakData &&
        other.date.year == date.year &&
        other.date.month == date.month &&
        other.date.day == date.day;
  }

  @override
  int get hashCode {
    return Object.hash(date.year, date.month, date.day);
  }

  @override
  String toString() {
    return 'EnhancedStreakData(date: $date, totalTasks: $totalTasks, completedTasks: $completedTasks, allTasksCompleted: $allTasksCompleted, streakValid: $streakValid)';
  }
}

/// Task completion record model
class TaskCompletion {
  final int? id;
  final String taskId;
  final DateTime completedAt;
  final DateTime assignedDate;

  const TaskCompletion({
    this.id,
    required this.taskId,
    required this.completedAt,
    required this.assignedDate,
  });

  /// Create from database map
  factory TaskCompletion.fromMap(Map<String, dynamic> map) {
    return TaskCompletion(
      id: map['id'] as int?,
      taskId: map['task_id'] as String,
      completedAt: DateTime.fromMillisecondsSinceEpoch((map['completed_at'] as int) * 1000),
      assignedDate: DateTime.fromMillisecondsSinceEpoch((map['assigned_date'] as int) * 1000),
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'task_id': taskId,
      'completed_at': completedAt.millisecondsSinceEpoch ~/ 1000,
      'assigned_date': assignedDate.millisecondsSinceEpoch ~/ 1000,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaskCompletion && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'TaskCompletion(taskId: $taskId, completedAt: $completedAt, assignedDate: $assignedDate)';
  }
}

/// Streak statistics model
class StreakStatistics {
  final int currentStreak;
  final int longestStreak;
  final int totalCompletedDays;
  final int totalDaysTracked;
  final double averageTasksPerDay;
  final double completionRate;
  final DateTime? lastCompletedDate;

  const StreakStatistics({
    required this.currentStreak,
    required this.longestStreak,
    required this.totalCompletedDays,
    required this.totalDaysTracked,
    required this.averageTasksPerDay,
    required this.completionRate,
    this.lastCompletedDate,
  });

  /// Create empty statistics
  factory StreakStatistics.empty() {
    return const StreakStatistics(
      currentStreak: 0,
      longestStreak: 0,
      totalCompletedDays: 0,
      totalDaysTracked: 0,
      averageTasksPerDay: 0.0,
      completionRate: 0.0,
    );
  }

  @override
  String toString() {
    return 'StreakStatistics(currentStreak: $currentStreak, longestStreak: $longestStreak, completionRate: ${(completionRate * 100).toStringAsFixed(1)}%)';
  }
}
