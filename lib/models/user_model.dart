class UserModel {
  final String uid;
  final String emailAddress;
  final String photoURL;
  final String username;
  final bool isPremium;
  final DateTime? createdAt;
  final DateTime? lastLogin;

  UserModel({
    required this.uid,
    required String emailAddress,
    required this.photoURL,
    required this.username,
    required this.isPremium,
    this.createdAt,
    this.lastLogin,
  })  : emailAddress = emailAddress,
        assert(RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(emailAddress),
            'Invalid email format');

  factory UserModel.fromFirebase(Map<String, dynamic> userData) {
    if (userData['uid'] == null || userData['email'] == null) {
      throw Exception('Missing required user data');
    }
    return UserModel(
      uid: userData['uid'],
      emailAddress: userData['email'],
      photoURL: userData['photoURL'] ?? '',
      username: userData['username'] ?? 'Anonymous',
      isPremium: userData['isPremium'] ?? false,
      createdAt: userData['createdAt'] != null
          ? DateTime.parse(userData['createdAt'])
          : null,
      lastLogin: userData['lastLogin'] != null
          ? DateTime.parse(userData['lastLogin'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'email': emailAddress,
      'photoURL': photoURL,
      'username': username,
      'isPremium': isPremium,
      'createdAt': createdAt?.toIso8601String(),
      'lastLogin': lastLogin?.toIso8601String(),
    };
  }

  UserModel copyWith({
    String? uid,
    String? emailAddress,
    String? photoURL,
    String? username,
    bool? isPremium,
    DateTime? createdAt,
    DateTime? lastLogin,
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      emailAddress: emailAddress ?? this.emailAddress,
      photoURL: photoURL ?? this.photoURL,
      username: username ?? this.username,
      isPremium: isPremium ?? this.isPremium,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
    );
  }
}
