import 'dart:convert';
import 'package:flutter/material.dart';

/// Enhanced task priority enum with database mapping
enum TaskPriority {
  low(0, 'Low', Color(0xFF10B981)),
  medium(1, 'Medium', Color(0xFF3B82F6)),
  high(2, 'High', Color(0xFFF59E0B)),
  urgent(3, 'Urgent', Color(0xFFEF4444));

  const TaskPriority(this.value, this.label, this.color);

  final int value;
  final String label;
  final Color color;

  static TaskPriority fromValue(int value) {
    return TaskPriority.values.firstWhere(
      (priority) => priority.value == value,
      orElse: () => TaskPriority.medium,
    );
  }
}

/// Enhanced task model with database support and date assignment
class EnhancedTask {
  final String id;
  final String title;
  final String description;
  final TaskPriority priority;
  final DateTime createdAt;
  final DateTime? dueDate;
  final DateTime assignedDate; // New: for today/tomorrow assignment
  final bool isCompleted;
  final DateTime? completedAt;
  final int displayOrder; // New: for drag-and-drop ordering
  final List<String> tags;
  final Color? customColor;
  final DateTime updatedAt;

  const EnhancedTask({
    required this.id,
    required this.title,
    required this.description,
    required this.priority,
    required this.createdAt,
    required this.assignedDate,
    this.dueDate,
    this.isCompleted = false,
    this.completedAt,
    this.displayOrder = 0,
    this.tags = const [],
    this.customColor,
    required this.updatedAt,
  });

  /// Create task from database map
  factory EnhancedTask.fromMap(Map<String, dynamic> map) {
    return EnhancedTask(
      id: map['id'] as String,
      title: map['title'] as String,
      description: map['description'] as String? ?? '',
      priority: TaskPriority.fromValue(map['priority'] as int),
      createdAt: DateTime.fromMillisecondsSinceEpoch(
          (map['created_at'] as int) * 1000),
      dueDate: map['due_date'] != null
          ? DateTime.fromMillisecondsSinceEpoch((map['due_date'] as int) * 1000)
          : null,
      assignedDate: DateTime.fromMillisecondsSinceEpoch(
          (map['assigned_date'] as int) * 1000),
      isCompleted: (map['is_completed'] as int) == 1,
      completedAt: map['completed_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(
              (map['completed_at'] as int) * 1000)
          : null,
      displayOrder: map['display_order'] as int? ?? 0,
      tags: map['tags'] != null
          ? List<String>.from(json.decode(map['tags'] as String))
          : [],
      customColor: map['custom_color'] != null
          ? Color(int.parse(map['custom_color'] as String, radix: 16))
          : null,
      updatedAt: DateTime.fromMillisecondsSinceEpoch(
          (map['updated_at'] as int) * 1000),
    );
  }

  /// Convert task to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'priority': priority.value,
      'created_at': createdAt.millisecondsSinceEpoch ~/ 1000,
      'due_date': dueDate?.millisecondsSinceEpoch != null
          ? dueDate!.millisecondsSinceEpoch ~/ 1000
          : null,
      'assigned_date': assignedDate.millisecondsSinceEpoch ~/ 1000,
      'is_completed': isCompleted ? 1 : 0,
      'completed_at': completedAt?.millisecondsSinceEpoch != null
          ? completedAt!.millisecondsSinceEpoch ~/ 1000
          : null,
      'display_order': displayOrder,
      'tags': tags.isNotEmpty ? json.encode(tags) : null,
      'custom_color': customColor?.value.toRadixString(16),
      'updated_at': updatedAt.millisecondsSinceEpoch ~/ 1000,
    };
  }

  /// Create a copy with updated fields
  EnhancedTask copyWith({
    String? id,
    String? title,
    String? description,
    TaskPriority? priority,
    DateTime? createdAt,
    DateTime? dueDate,
    DateTime? assignedDate,
    bool? isCompleted,
    DateTime? completedAt,
    int? displayOrder,
    List<String>? tags,
    Color? customColor,
    DateTime? updatedAt,
  }) {
    return EnhancedTask(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      priority: priority ?? this.priority,
      createdAt: createdAt ?? this.createdAt,
      dueDate: dueDate ?? this.dueDate,
      assignedDate: assignedDate ?? this.assignedDate,
      isCompleted: isCompleted ?? this.isCompleted,
      completedAt: completedAt ?? this.completedAt,
      displayOrder: displayOrder ?? this.displayOrder,
      tags: tags ?? this.tags,
      customColor: customColor ?? this.customColor,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Check if task is assigned to today
  bool get isAssignedToday {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final taskDate =
        DateTime(assignedDate.year, assignedDate.month, assignedDate.day);
    return taskDate.isAtSameMomentAs(today);
  }

  /// Check if task is assigned to tomorrow
  bool get isAssignedTomorrow {
    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    final taskDate =
        DateTime(assignedDate.year, assignedDate.month, assignedDate.day);
    return taskDate.isAtSameMomentAs(tomorrow);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EnhancedTask && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'EnhancedTask(id: $id, title: $title, isCompleted: $isCompleted, assignedDate: $assignedDate)';
  }
}

/// Task sorting options
enum TaskSortOption {
  dateCreated('Date Created'),
  priority('Priority'),
  alphabetical('Alphabetical'),
  dueDate('Due Date'),
  displayOrder('Custom Order');

  const TaskSortOption(this.label);
  final String label;
}

/// Task filter options
enum TaskFilter {
  all('All Tasks'),
  today('Today'),
  tomorrow('Tomorrow'),
  completed('Completed'),
  pending('Pending');

  const TaskFilter(this.label);
  final String label;
}
