import 'package:unstack/database/database_helper.dart';
import 'package:unstack/models/enhanced_streak_data.dart';
import 'package:unstack/models/enhanced_task.dart';
import 'package:unstack/utils/app_logger.dart';

/// Repository for streak-related database operations
class StreakRepository {
  final DatabaseHelper _dbHelper;

  StreakRepository({DatabaseHelper? dbHelper}) : _dbHelper = dbHelper ?? DatabaseHelper();

  /// Create or update streak data for a specific date
  Future<bool> upsertStreakData(EnhancedStreakData streakData) async {
    try {
      final startOfDay = DateTime(streakData.date.year, streakData.date.month, streakData.date.day);
      final existingData = await getStreakDataByDate(startOfDay);

      if (existingData != null) {
        // Update existing record
        final rowsAffected = await _dbHelper.update(
          'streak_data',
          streakData.copyWith(id: existingData.id).toMap(),
          'id = ?',
          [existingData.id],
        );
        return rowsAffected > 0;
      } else {
        // Insert new record
        await _dbHelper.insert('streak_data', streakData.toMap());
        return true;
      }
    } catch (e) {
      AppLogger.error('Failed to upsert streak data: $e');
      rethrow;
    }
  }

  /// Get streak data for a specific date
  Future<EnhancedStreakData?> getStreakDataByDate(DateTime date) async {
    try {
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final results = await _dbHelper.query(
        'streak_data',
        where: 'date >= ? AND date < ?',
        whereArgs: [
          startOfDay.millisecondsSinceEpoch ~/ 1000,
          endOfDay.millisecondsSinceEpoch ~/ 1000,
        ],
        limit: 1,
      );

      if (results.isNotEmpty) {
        return EnhancedStreakData.fromMap(results.first);
      }
      return null;
    } catch (e) {
      AppLogger.error('Failed to get streak data by date: $e');
      rethrow;
    }
  }

  /// Get all streak data
  Future<List<EnhancedStreakData>> getAllStreakData() async {
    try {
      final results = await _dbHelper.query(
        'streak_data',
        orderBy: 'date DESC',
      );

      return results.map((map) => EnhancedStreakData.fromMap(map)).toList();
    } catch (e) {
      AppLogger.error('Failed to get all streak data: $e');
      rethrow;
    }
  }

  /// Get streak data for a date range
  Future<List<EnhancedStreakData>> getStreakDataInRange(DateTime startDate, DateTime endDate) async {
    try {
      final results = await _dbHelper.query(
        'streak_data',
        where: 'date >= ? AND date <= ?',
        whereArgs: [
          startDate.millisecondsSinceEpoch ~/ 1000,
          endDate.millisecondsSinceEpoch ~/ 1000,
        ],
        orderBy: 'date ASC',
      );

      return results.map((map) => EnhancedStreakData.fromMap(map)).toList();
    } catch (e) {
      AppLogger.error('Failed to get streak data in range: $e');
      rethrow;
    }
  }

  /// Calculate and update streak data for a specific date based on tasks
  Future<EnhancedStreakData> calculateAndUpdateStreakData(DateTime date, List<EnhancedTask> tasks) async {
    try {
      final totalTasks = tasks.length;
      final completedTasks = tasks.where((task) => task.isCompleted).length;
      final allTasksCompleted = totalTasks > 0 && completedTasks == totalTasks;

      final streakData = EnhancedStreakData(
        date: date,
        totalTasks: totalTasks,
        completedTasks: completedTasks,
        allTasksCompleted: allTasksCompleted,
        streakValid: allTasksCompleted,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await upsertStreakData(streakData);
      AppLogger.info('Streak data calculated and updated for $date');
      return streakData;
    } catch (e) {
      AppLogger.error('Failed to calculate and update streak data: $e');
      rethrow;
    }
  }

  /// Get current streak count
  Future<int> getCurrentStreak() async {
    try {
      final allData = await getAllStreakData();
      if (allData.isEmpty) return 0;

      // Sort by date descending (most recent first)
      allData.sort((a, b) => b.date.compareTo(a.date));

      int streak = 0;
      final today = DateTime.now();
      final todayStart = DateTime(today.year, today.month, today.day);

      // Check if we should start counting from today or yesterday
      DateTime checkDate = todayStart;
      final todayData = allData.firstWhere(
        (data) => _isSameDay(data.date, todayStart),
        orElse: () => EnhancedStreakData(
          date: todayStart,
          totalTasks: 0,
          completedTasks: 0,
          allTasksCompleted: false,
          streakValid: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      );

      // If today is not complete, start from yesterday
      if (!todayData.streakValid) {
        checkDate = todayStart.subtract(const Duration(days: 1));
      }

      // Count consecutive valid streak days
      for (final data in allData) {
        if (_isSameDay(data.date, checkDate) && data.streakValid) {
          streak++;
          checkDate = checkDate.subtract(const Duration(days: 1));
        } else if (_isSameDay(data.date, checkDate)) {
          break; // Found a non-valid day, streak ends
        }
      }

      return streak;
    } catch (e) {
      AppLogger.error('Failed to get current streak: $e');
      return 0;
    }
  }

  /// Get longest streak ever achieved
  Future<int> getLongestStreak() async {
    try {
      final allData = await getAllStreakData();
      if (allData.isEmpty) return 0;

      // Sort by date ascending
      allData.sort((a, b) => a.date.compareTo(b.date));

      int maxStreak = 0;
      int currentStreakCount = 0;
      DateTime? lastDate;

      for (final data in allData) {
        if (data.streakValid) {
          if (lastDate == null || _isConsecutiveDay(lastDate, data.date)) {
            currentStreakCount++;
            maxStreak = maxStreak > currentStreakCount ? maxStreak : currentStreakCount;
          } else {
            currentStreakCount = 1;
          }
          lastDate = data.date;
        } else {
          currentStreakCount = 0;
          lastDate = null;
        }
      }

      return maxStreak;
    } catch (e) {
      AppLogger.error('Failed to get longest streak: $e');
      return 0;
    }
  }

  /// Get streak statistics
  Future<StreakStatistics> getStreakStatistics() async {
    try {
      final allData = await getAllStreakData();
      final currentStreak = await getCurrentStreak();
      final longestStreak = await getLongestStreak();

      if (allData.isEmpty) {
        return StreakStatistics.empty();
      }

      final totalCompletedDays = allData.where((data) => data.streakValid).length;
      final totalDaysTracked = allData.length;
      final totalTasks = allData.fold<int>(0, (sum, data) => sum + data.totalTasks);
      final averageTasksPerDay = totalDaysTracked > 0 ? totalTasks / totalDaysTracked : 0.0;
      final completionRate = totalDaysTracked > 0 ? totalCompletedDays / totalDaysTracked : 0.0;

      final lastCompletedData = allData
          .where((data) => data.streakValid)
          .fold<EnhancedStreakData?>(null, (latest, data) {
        if (latest == null || data.date.isAfter(latest.date)) {
          return data;
        }
        return latest;
      });

      return StreakStatistics(
        currentStreak: currentStreak,
        longestStreak: longestStreak,
        totalCompletedDays: totalCompletedDays,
        totalDaysTracked: totalDaysTracked,
        averageTasksPerDay: averageTasksPerDay,
        completionRate: completionRate,
        lastCompletedDate: lastCompletedData?.date,
      );
    } catch (e) {
      AppLogger.error('Failed to get streak statistics: $e');
      return StreakStatistics.empty();
    }
  }

  /// Get task completions for a specific date
  Future<List<TaskCompletion>> getTaskCompletions(DateTime date) async {
    try {
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final results = await _dbHelper.query(
        'task_completions',
        where: 'assigned_date >= ? AND assigned_date < ?',
        whereArgs: [
          startOfDay.millisecondsSinceEpoch ~/ 1000,
          endOfDay.millisecondsSinceEpoch ~/ 1000,
        ],
        orderBy: 'completed_at DESC',
      );

      return results.map((map) => TaskCompletion.fromMap(map)).toList();
    } catch (e) {
      AppLogger.error('Failed to get task completions: $e');
      rethrow;
    }
  }

  /// Delete streak data (for testing/reset)
  Future<bool> clearAllStreakData() async {
    try {
      await _dbHelper.rawExecute('DELETE FROM streak_data');
      await _dbHelper.rawExecute('DELETE FROM task_completions');
      AppLogger.info('All streak data cleared');
      return true;
    } catch (e) {
      AppLogger.error('Failed to clear streak data: $e');
      rethrow;
    }
  }

  /// Helper method to check if two dates are the same day
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  /// Helper method to check if two dates are consecutive days
  bool _isConsecutiveDay(DateTime date1, DateTime date2) {
    final nextDay = DateTime(date1.year, date1.month, date1.day + 1);
    return _isSameDay(nextDay, date2);
  }
}
