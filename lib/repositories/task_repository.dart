import 'package:unstack/database/database_helper.dart';
import 'package:unstack/models/enhanced_task.dart';
import 'package:unstack/utils/app_logger.dart';

/// Repository for task-related database operations
class TaskRepository {
  final DatabaseHelper _dbHelper;

  TaskRepository({DatabaseHelper? dbHelper})
      : _dbHelper = dbHelper ?? DatabaseHelper();

  /// Create a new task
  Future<String> createTask(EnhancedTask task) async {
    try {
      await _dbHelper.insert('tasks', task.toMap());
      AppLogger.info('Task created: ${task.id}');
      return task.id;
    } catch (e) {
      AppLogger.error('Failed to create task: $e');
      rethrow;
    }
  }

  /// Get task by ID
  Future<EnhancedTask?> getTaskById(String id) async {
    try {
      final results = await _dbHelper.query(
        'tasks',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (results.isNotEmpty) {
        return EnhancedTask.fromMap(results.first);
      }
      return null;
    } catch (e) {
      AppLogger.error('Failed to get task by ID: $e');
      rethrow;
    }
  }

  /// Get all tasks
  Future<List<EnhancedTask>> getAllTasks() async {
    try {
      final results = await _dbHelper.query(
        'tasks',
        orderBy: 'display_order ASC, created_at DESC',
      );

      return results.map((map) => EnhancedTask.fromMap(map)).toList();
    } catch (e) {
      AppLogger.error('Failed to get all tasks: $e');
      rethrow;
    }
  }

  /// Get tasks by assigned date
  Future<List<EnhancedTask>> getTasksByAssignedDate(DateTime date) async {
    try {
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final results = await _dbHelper.query(
        'tasks',
        where: 'assigned_date >= ? AND assigned_date < ?',
        whereArgs: [
          startOfDay.millisecondsSinceEpoch ~/ 1000,
          endOfDay.millisecondsSinceEpoch ~/ 1000,
        ],
        orderBy: 'display_order ASC, created_at DESC',
      );

      return results.map((map) => EnhancedTask.fromMap(map)).toList();
    } catch (e) {
      AppLogger.error('Failed to get tasks by assigned date: $e');
      rethrow;
    }
  }

  /// Get today's tasks
  Future<List<EnhancedTask>> getTodaysTasks() async {
    return getTasksByAssignedDate(DateTime.now());
  }

  /// Get tomorrow's tasks
  Future<List<EnhancedTask>> getTomorrowsTasks() async {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return getTasksByAssignedDate(tomorrow);
  }

  /// Get completed tasks
  Future<List<EnhancedTask>> getCompletedTasks() async {
    try {
      final results = await _dbHelper.query(
        'tasks',
        where: 'is_completed = ?',
        whereArgs: [1],
        orderBy: 'completed_at DESC',
      );

      return results.map((map) => EnhancedTask.fromMap(map)).toList();
    } catch (e) {
      AppLogger.error('Failed to get completed tasks: $e');
      rethrow;
    }
  }

  /// Get pending tasks
  Future<List<EnhancedTask>> getPendingTasks() async {
    try {
      final results = await _dbHelper.query(
        'tasks',
        where: 'is_completed = ?',
        whereArgs: [0],
        orderBy: 'display_order ASC, created_at DESC',
      );

      return results.map((map) => EnhancedTask.fromMap(map)).toList();
    } catch (e) {
      AppLogger.error('Failed to get pending tasks: $e');
      rethrow;
    }
  }

  /// Update task
  Future<bool> updateTask(EnhancedTask task) async {
    try {
      final rowsAffected = await _dbHelper.update(
        'tasks',
        task.toMap(),
        'id = ?',
        [task.id],
      );

      AppLogger.info('Task updated: ${task.id}');
      return rowsAffected > 0;
    } catch (e) {
      AppLogger.error('Failed to update task: $e');
      rethrow;
    }
  }

  /// Toggle task completion
  Future<bool> toggleTaskCompletion(String taskId, bool isCompleted) async {
    try {
      final now = DateTime.now();
      final updateData = {
        'is_completed': isCompleted ? 1 : 0,
        'completed_at': isCompleted ? now.millisecondsSinceEpoch ~/ 1000 : null,
        'updated_at': now.millisecondsSinceEpoch ~/ 1000,
      };

      final rowsAffected = await _dbHelper.update(
        'tasks',
        updateData,
        'id = ?',
        [taskId],
      );

      AppLogger.info('Task completion toggled: $taskId -> $isCompleted');
      return rowsAffected > 0;
    } catch (e) {
      AppLogger.error('Failed to toggle task completion: $e');
      rethrow;
    }
  }

  /// Update task display order (for drag-and-drop)
  Future<bool> updateTaskOrder(String taskId, int newOrder) async {
    try {
      final rowsAffected = await _dbHelper.update(
        'tasks',
        {
          'display_order': newOrder,
          'updated_at': DateTime.now().millisecondsSinceEpoch ~/ 1000,
        },
        'id = ?',
        [taskId],
      );

      return rowsAffected > 0;
    } catch (e) {
      AppLogger.error('Failed to update task order: $e');
      rethrow;
    }
  }

  /// Reorder tasks (batch update for drag-and-drop)
  Future<bool> reorderTasks(List<String> taskIds) async {
    try {
      return await _dbHelper.transaction((txn) async {
        for (int i = 0; i < taskIds.length; i++) {
          await txn.update(
            'tasks',
            {
              'display_order': i,
              'updated_at': DateTime.now().millisecondsSinceEpoch ~/ 1000,
            },
            where: 'id = ?',
            whereArgs: [taskIds[i]],
          );
        }
        return true;
      });
    } catch (e) {
      AppLogger.error('Failed to reorder tasks: $e');
      rethrow;
    }
  }

  /// Delete task
  Future<bool> deleteTask(String taskId) async {
    try {
      final rowsAffected = await _dbHelper.delete(
        'tasks',
        'id = ?',
        [taskId],
      );

      AppLogger.info('Task deleted: $taskId');
      return rowsAffected > 0;
    } catch (e) {
      AppLogger.error('Failed to delete task: $e');
      rethrow;
    }
  }

  /// Get tasks by priority
  Future<List<EnhancedTask>> getTasksByPriority(TaskPriority priority) async {
    try {
      final results = await _dbHelper.query(
        'tasks',
        where: 'priority = ?',
        whereArgs: [priority.value],
        orderBy: 'display_order ASC, created_at DESC',
      );

      return results.map((map) => EnhancedTask.fromMap(map)).toList();
    } catch (e) {
      AppLogger.error('Failed to get tasks by priority: $e');
      rethrow;
    }
  }

  /// Search tasks by title or description
  Future<List<EnhancedTask>> searchTasks(String query) async {
    try {
      final results = await _dbHelper.query(
        'tasks',
        where: 'title LIKE ? OR description LIKE ?',
        whereArgs: ['%$query%', '%$query%'],
        orderBy: 'display_order ASC, created_at DESC',
      );

      return results.map((map) => EnhancedTask.fromMap(map)).toList();
    } catch (e) {
      AppLogger.error('Failed to search tasks: $e');
      rethrow;
    }
  }

  /// Get task count by status
  Future<Map<String, int>> getTaskCounts() async {
    try {
      final totalResult =
          await _dbHelper.rawQuery('SELECT COUNT(*) as count FROM tasks');
      final completedResult = await _dbHelper.rawQuery(
          'SELECT COUNT(*) as count FROM tasks WHERE is_completed = 1');
      final pendingResult = await _dbHelper.rawQuery(
          'SELECT COUNT(*) as count FROM tasks WHERE is_completed = 0');
      final todayResult = await _dbHelper.rawQuery('''
        SELECT COUNT(*) as count FROM tasks 
        WHERE assigned_date >= ? AND assigned_date < ?
      ''', [
        DateTime.now().millisecondsSinceEpoch ~/ 1000,
        DateTime.now().add(const Duration(days: 1)).millisecondsSinceEpoch ~/
            1000,
      ]);

      return {
        'total': totalResult.first['count'] as int,
        'completed': completedResult.first['count'] as int,
        'pending': pendingResult.first['count'] as int,
        'today': todayResult.first['count'] as int,
      };
    } catch (e) {
      AppLogger.error('Failed to get task counts: $e');
      rethrow;
    }
  }

  /// Clear all tasks (for testing/reset)
  Future<bool> clearAllTasks() async {
    try {
      await _dbHelper.rawExecute('DELETE FROM tasks');
      AppLogger.info('All tasks cleared');
      return true;
    } catch (e) {
      AppLogger.error('Failed to clear all tasks: $e');
      rethrow;
    }
  }

  /// Get next display order for new tasks
  Future<int> getNextDisplayOrder() async {
    try {
      final result = await _dbHelper
          .rawQuery('SELECT MAX(display_order) as max_order FROM tasks');
      final maxOrder = result.first['max_order'] as int?;
      return (maxOrder ?? -1) + 1;
    } catch (e) {
      AppLogger.error('Failed to get next display order: $e');
      return 0;
    }
  }

  /// Move task to different date
  Future<bool> moveTaskToDate(String taskId, DateTime newDate) async {
    try {
      final startOfDay = DateTime(newDate.year, newDate.month, newDate.day);
      final rowsAffected = await _dbHelper.update(
        'tasks',
        {
          'assigned_date': startOfDay.millisecondsSinceEpoch ~/ 1000,
          'updated_at': DateTime.now().millisecondsSinceEpoch ~/ 1000,
        },
        'id = ?',
        [taskId],
      );

      AppLogger.info('Task moved to date: $taskId -> $newDate');
      return rowsAffected > 0;
    } catch (e) {
      AppLogger.error('Failed to move task to date: $e');
      rethrow;
    }
  }
}
