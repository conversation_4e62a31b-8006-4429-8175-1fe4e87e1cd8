import 'dart:io';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:unstack/utils/app_logger.dart';

/// SQLite database helper for the todo app
class DatabaseHelper {
  static const String _databaseName = 'unstack_todo.db';
  static const int _databaseVersion = 1;

  static Database? _database;
  static final DatabaseHelper _instance = DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  /// Get database instance
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  /// Initialize database
  Future<Database> _initDatabase() async {
    try {
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, _databaseName);

      AppLogger.info('Initializing database at: $path');

      return await openDatabase(
        path,
        version: _databaseVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
        onConfigure: _onConfigure,
      );
    } catch (e) {
      AppLogger.error('Failed to initialize database: $e');
      rethrow;
    }
  }

  /// Configure database settings
  Future<void> _onConfigure(Database db) async {
    // Enable foreign key constraints
    await db.execute('PRAGMA foreign_keys = ON');
    AppLogger.debug('Database configured with foreign key constraints');
  }

  /// Create database tables
  Future<void> _onCreate(Database db, int version) async {
    try {
      AppLogger.info('Creating database tables...');

      // Execute schema statements directly
      await _createTables(db);

      AppLogger.info('Database tables created successfully');
    } catch (e) {
      AppLogger.error('Failed to create database tables: $e');
      rethrow;
    }
  }

  /// Create all database tables
  Future<void> _createTables(Database db) async {
    // Tasks table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS tasks (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT NOT NULL DEFAULT '',
        priority INTEGER NOT NULL DEFAULT 1,
        created_at INTEGER NOT NULL,
        due_date INTEGER,
        assigned_date INTEGER NOT NULL,
        is_completed INTEGER NOT NULL DEFAULT 0,
        completed_at INTEGER,
        display_order INTEGER NOT NULL DEFAULT 0,
        tags TEXT,
        custom_color TEXT,
        updated_at INTEGER NOT NULL
      )
    ''');

    // Streak data table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS streak_data (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date INTEGER NOT NULL UNIQUE,
        total_tasks INTEGER NOT NULL DEFAULT 0,
        completed_tasks INTEGER NOT NULL DEFAULT 0,
        all_tasks_completed INTEGER NOT NULL DEFAULT 0,
        streak_valid INTEGER NOT NULL DEFAULT 0,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL
      )
    ''');

    // Task completions table
    await db.execute('''
      CREATE TABLE IF NOT EXISTS task_completions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        task_id TEXT NOT NULL,
        completed_at INTEGER NOT NULL,
        assigned_date INTEGER NOT NULL,
        FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE
      )
    ''');

    // Create indexes
    await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_tasks_assigned_date ON tasks (assigned_date)');
    await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_tasks_is_completed ON tasks (is_completed)');
    await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_tasks_display_order ON tasks (display_order)');
    await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON tasks (due_date)');
    await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_streak_data_date ON streak_data (date)');
    await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_task_completions_task_id ON task_completions (task_id)');
    await db.execute(
        'CREATE INDEX IF NOT EXISTS idx_task_completions_assigned_date ON task_completions (assigned_date)');

    // Create triggers
    await db.execute('''
      CREATE TRIGGER IF NOT EXISTS update_tasks_timestamp
        AFTER UPDATE ON tasks
        FOR EACH ROW
      BEGIN
        UPDATE tasks SET updated_at = strftime('%s', 'now') WHERE id = NEW.id;
      END
    ''');

    await db.execute('''
      CREATE TRIGGER IF NOT EXISTS update_streak_data_timestamp
        AFTER UPDATE ON streak_data
        FOR EACH ROW
      BEGIN
        UPDATE streak_data SET updated_at = strftime('%s', 'now') WHERE id = NEW.id;
      END
    ''');

    await db.execute('''
      CREATE TRIGGER IF NOT EXISTS create_task_completion
        AFTER UPDATE OF is_completed ON tasks
        FOR EACH ROW
        WHEN NEW.is_completed = 1 AND OLD.is_completed = 0
      BEGIN
        INSERT INTO task_completions (task_id, completed_at, assigned_date)
        VALUES (NEW.id, strftime('%s', 'now'), NEW.assigned_date);
      END
    ''');

    await db.execute('''
      CREATE TRIGGER IF NOT EXISTS remove_task_completion
        AFTER UPDATE OF is_completed ON tasks
        FOR EACH ROW
        WHEN NEW.is_completed = 0 AND OLD.is_completed = 1
      BEGIN
        DELETE FROM task_completions
        WHERE task_id = NEW.id
        AND assigned_date = NEW.assigned_date;
      END
    ''');
  }

  /// Handle database upgrades
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    AppLogger.info(
        'Upgrading database from version $oldVersion to $newVersion');

    // Handle future database migrations here
    if (oldVersion < 2) {
      // Example migration for version 2
      // await db.execute('ALTER TABLE tasks ADD COLUMN new_field TEXT');
    }
  }

  /// Execute a raw SQL query
  Future<List<Map<String, dynamic>>> rawQuery(String sql,
      [List<dynamic>? arguments]) async {
    try {
      final db = await database;
      return await db.rawQuery(sql, arguments);
    } catch (e) {
      AppLogger.error('Raw query failed: $sql, Error: $e');
      rethrow;
    }
  }

  /// Execute a raw SQL statement
  Future<int> rawExecute(String sql, [List<dynamic>? arguments]) async {
    try {
      final db = await database;
      return await db.rawUpdate(sql, arguments);
    } catch (e) {
      AppLogger.error('Raw execute failed: $sql, Error: $e');
      rethrow;
    }
  }

  /// Insert data into a table
  Future<int> insert(String table, Map<String, dynamic> data) async {
    try {
      final db = await database;
      return await db.insert(table, data,
          conflictAlgorithm: ConflictAlgorithm.replace);
    } catch (e) {
      AppLogger.error('Insert failed for table $table: $e');
      rethrow;
    }
  }

  /// Update data in a table
  Future<int> update(String table, Map<String, dynamic> data, String where,
      List<dynamic> whereArgs) async {
    try {
      final db = await database;
      return await db.update(table, data, where: where, whereArgs: whereArgs);
    } catch (e) {
      AppLogger.error('Update failed for table $table: $e');
      rethrow;
    }
  }

  /// Delete data from a table
  Future<int> delete(
      String table, String where, List<dynamic> whereArgs) async {
    try {
      final db = await database;
      return await db.delete(table, where: where, whereArgs: whereArgs);
    } catch (e) {
      AppLogger.error('Delete failed for table $table: $e');
      rethrow;
    }
  }

  /// Query data from a table
  Future<List<Map<String, dynamic>>> query(
    String table, {
    List<String>? columns,
    String? where,
    List<dynamic>? whereArgs,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    try {
      final db = await database;
      return await db.query(
        table,
        columns: columns,
        where: where,
        whereArgs: whereArgs,
        orderBy: orderBy,
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      AppLogger.error('Query failed for table $table: $e');
      rethrow;
    }
  }

  /// Execute multiple operations in a transaction
  Future<T> transaction<T>(Future<T> Function(Transaction txn) action) async {
    try {
      final db = await database;
      return await db.transaction(action);
    } catch (e) {
      AppLogger.error('Transaction failed: $e');
      rethrow;
    }
  }

  /// Close database connection
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
      AppLogger.info('Database connection closed');
    }
  }

  /// Delete database file (for testing/reset purposes)
  Future<void> deleteDatabase() async {
    try {
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, _databaseName);

      if (await File(path).exists()) {
        await File(path).delete();
        AppLogger.info('Database file deleted');
      }

      _database = null;
    } catch (e) {
      AppLogger.error('Failed to delete database: $e');
      rethrow;
    }
  }

  /// Get database file size
  Future<int> getDatabaseSize() async {
    try {
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, _databaseName);
      final file = File(path);

      if (await file.exists()) {
        return await file.length();
      }
      return 0;
    } catch (e) {
      AppLogger.error('Failed to get database size: $e');
      return 0;
    }
  }

  /// Check if database exists
  Future<bool> databaseExists() async {
    try {
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, _databaseName);
      return await File(path).exists();
    } catch (e) {
      AppLogger.error('Failed to check database existence: $e');
      return false;
    }
  }
}
