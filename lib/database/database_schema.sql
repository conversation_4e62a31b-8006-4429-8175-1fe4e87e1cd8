-- Flutter Todo App Database Schema
-- SQLite database structure for task management and streak tracking

-- Tasks table with date assignment and ordering support
CREATE TABLE IF NOT EXISTS tasks (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL DEFAULT '',
    priority INTEGER NOT NULL DEFAULT 1, -- 0=low, 1=medium, 2=high, 3=urgent
    created_at INTEGER NOT NULL, -- Unix timestamp
    due_date INTEGER, -- Unix timestamp, nullable
    assigned_date INTEGER NOT NULL, -- Unix timestamp for today/tomorrow assignment
    is_completed INTEGER NOT NULL DEFAULT 0, -- 0=false, 1=true
    completed_at INTEGER, -- Unix timestamp when completed, nullable
    display_order INTEGER NOT NULL DEFAULT 0, -- For drag-and-drop ordering
    tags TEXT, -- JSON array of tags
    custom_color TEXT, -- Hex color code, nullable
    updated_at INTEGER NOT NULL -- Unix timestamp
);

-- Streak tracking table for daily completion data
CREATE TABLE IF NOT EXISTS streak_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date INTEGER NOT NULL UNIQUE, -- Unix timestamp (start of day)
    total_tasks INTEGER NOT NULL DEFAULT 0,
    completed_tasks INTEGER NOT NULL DEFAULT 0,
    all_tasks_completed INTEGER NOT NULL DEFAULT 0, -- 0=false, 1=true
    streak_valid INTEGER NOT NULL DEFAULT 0, -- 0=false, 1=true
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL
);

-- Task completion history for detailed tracking
CREATE TABLE IF NOT EXISTS task_completions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id TEXT NOT NULL,
    completed_at INTEGER NOT NULL, -- Unix timestamp
    assigned_date INTEGER NOT NULL, -- Date the task was assigned to
    FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE
);

-- Indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_tasks_assigned_date ON tasks (assigned_date);
CREATE INDEX IF NOT EXISTS idx_tasks_is_completed ON tasks (is_completed);
CREATE INDEX IF NOT EXISTS idx_tasks_display_order ON tasks (display_order);
CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON tasks (due_date);
CREATE INDEX IF NOT EXISTS idx_streak_data_date ON streak_data (date);
CREATE INDEX IF NOT EXISTS idx_task_completions_task_id ON task_completions (task_id);
CREATE INDEX IF NOT EXISTS idx_task_completions_assigned_date ON task_completions (assigned_date);

-- Triggers for automatic timestamp updates
CREATE TRIGGER IF NOT EXISTS update_tasks_timestamp 
    AFTER UPDATE ON tasks
    FOR EACH ROW
BEGIN
    UPDATE tasks SET updated_at = strftime('%s', 'now') WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_streak_data_timestamp 
    AFTER UPDATE ON streak_data
    FOR EACH ROW
BEGIN
    UPDATE streak_data SET updated_at = strftime('%s', 'now') WHERE id = NEW.id;
END;

-- Trigger to automatically create task completion record
CREATE TRIGGER IF NOT EXISTS create_task_completion 
    AFTER UPDATE OF is_completed ON tasks
    FOR EACH ROW
    WHEN NEW.is_completed = 1 AND OLD.is_completed = 0
BEGIN
    INSERT INTO task_completions (task_id, completed_at, assigned_date)
    VALUES (NEW.id, strftime('%s', 'now'), NEW.assigned_date);
END;

-- Trigger to remove task completion record when task is uncompleted
CREATE TRIGGER IF NOT EXISTS remove_task_completion 
    AFTER UPDATE OF is_completed ON tasks
    FOR EACH ROW
    WHEN NEW.is_completed = 0 AND OLD.is_completed = 1
BEGIN
    DELETE FROM task_completions 
    WHERE task_id = NEW.id 
    AND assigned_date = NEW.assigned_date;
END;
