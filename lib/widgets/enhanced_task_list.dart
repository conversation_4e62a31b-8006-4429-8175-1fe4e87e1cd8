import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:unstack/models/enhanced_task.dart';
import 'package:unstack/theme/app_theme.dart';
import 'package:unstack/widgets/enhanced_task_card.dart';
import 'package:unstack/widgets/glassmorphism_container.dart';

/// Enhanced task list with drag-and-drop reordering
class EnhancedTaskList extends StatefulWidget {
  final List<EnhancedTask> tasks;
  final ValueChanged<EnhancedTask>? onTaskTap;
  final ValueChanged<EnhancedTask>? onTaskToggle;
  final ValueChanged<EnhancedTask>? onTaskDelete;
  final ValueChanged<EnhancedTask>? onTaskEdit;
  final ValueChanged<List<EnhancedTask>>? onTasksReordered;
  final bool allowReordering;
  final bool showCompletedTasks;
  final String emptyMessage;

  const EnhancedTaskList({
    super.key,
    required this.tasks,
    this.onTaskTap,
    this.onTaskToggle,
    this.onTaskDelete,
    this.onTaskEdit,
    this.onTasksReordered,
    this.allowReordering = true,
    this.showCompletedTasks = true,
    this.emptyMessage = 'No tasks available',
  });

  @override
  State<EnhancedTaskList> createState() => _EnhancedTaskListState();
}

class _EnhancedTaskListState extends State<EnhancedTaskList> {
  late List<EnhancedTask> _displayTasks;

  @override
  void initState() {
    super.initState();
    _updateDisplayTasks();
  }

  @override
  void didUpdateWidget(EnhancedTaskList oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.tasks != widget.tasks ||
        oldWidget.showCompletedTasks != widget.showCompletedTasks) {
      _updateDisplayTasks();
    }
  }

  void _updateDisplayTasks() {
    _displayTasks = widget.showCompletedTasks
        ? List.from(widget.tasks)
        : widget.tasks.where((task) => !task.isCompleted).toList();
  }

  @override
  Widget build(BuildContext context) {
    if (_displayTasks.isEmpty) {
      return _buildEmptyState();
    }

    if (widget.allowReordering) {
      return _buildReorderableList();
    } else {
      return _buildRegularList();
    }
  }

  Widget _buildEmptyState() {
    return Center(
      child: GlassmorphismContainer(
        borderRadius: AppBorderRadius.xl,
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.xl),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.task_alt,
                color: AppColors.textSecondary,
                size: 48,
              ),
              const SizedBox(height: AppSpacing.md),
              Text(
                widget.emptyMessage,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildReorderableList() {
    return ReorderableListView.builder(
      onReorder: _handleReorder,
      padding: const EdgeInsets.only(bottom: AppSpacing.lg),
      buildDefaultDragHandles: false,
      proxyDecorator: _buildDragProxy,
      itemCount: _displayTasks.length,
      itemBuilder: (context, index) {
        final task = _displayTasks[index];
        return _buildTaskItem(task, index, true);
      },
    );
  }

  Widget _buildRegularList() {
    return ListView.builder(
      padding: const EdgeInsets.only(bottom: AppSpacing.lg),
      itemCount: _displayTasks.length,
      itemBuilder: (context, index) {
        final task = _displayTasks[index];
        return _buildTaskItem(task, index, false);
      },
    );
  }

  Widget _buildTaskItem(EnhancedTask task, int index, bool isReorderable) {
    Widget taskCard = EnhancedTaskCard(
      key: ValueKey(task.id),
      task: task,
      onTap: () => widget.onTaskTap?.call(task),
      onToggleComplete: (isCompleted) {
        final updatedTask = task.copyWith(
          isCompleted: isCompleted,
          completedAt: isCompleted ? DateTime.now() : null,
        );
        widget.onTaskToggle?.call(updatedTask);
      },
    );

    if (isReorderable) {
      taskCard = ReorderableDragStartListener(
        index: index,
        child: taskCard,
      );
    }

    // Wrap with Slidable for swipe actions
    return Slidable(
      key: ValueKey(task.id),
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        children: [
          // Edit action
          SlidableAction(
            onPressed: (context) {
              HapticFeedback.mediumImpact();
              widget.onTaskEdit?.call(task);
            },
            backgroundColor: AppColors.accentBlue,
            foregroundColor: Colors.white,
            icon: Icons.edit,
            label: 'Edit',
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(AppBorderRadius.lg),
              bottomLeft: Radius.circular(AppBorderRadius.lg),
            ),
          ),
          
          // Delete action
          SlidableAction(
            onPressed: (context) async {
              HapticFeedback.heavyImpact();
              final shouldDelete = await _showDeleteConfirmation(context, task);
              if (shouldDelete) {
                widget.onTaskDelete?.call(task);
              }
            },
            backgroundColor: AppColors.statusError,
            foregroundColor: Colors.white,
            icon: Icons.delete,
            label: 'Delete',
            borderRadius: const BorderRadius.only(
              topRight: Radius.circular(AppBorderRadius.lg),
              bottomRight: Radius.circular(AppBorderRadius.lg),
            ),
          ),
        ],
      ),
      child: taskCard,
    );
  }

  Widget _buildDragProxy(Widget child, int index, Animation<double> animation) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return Transform.scale(
          scale: 1.02,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(AppBorderRadius.xl),
              boxShadow: [
                BoxShadow(
                  color: AppColors.accentPurple.withValues(alpha: 0.3),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: child,
          ),
        );
      },
      child: child,
    );
  }

  void _handleReorder(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }

    setState(() {
      final task = _displayTasks.removeAt(oldIndex);
      _displayTasks.insert(newIndex, task);
    });

    // Update display order for all tasks
    final reorderedTasks = List<EnhancedTask>.from(_displayTasks);
    for (int i = 0; i < reorderedTasks.length; i++) {
      reorderedTasks[i] = reorderedTasks[i].copyWith(displayOrder: i);
    }

    widget.onTasksReordered?.call(reorderedTasks);
    HapticFeedback.mediumImpact();
  }

  Future<bool> _showDeleteConfirmation(BuildContext context, EnhancedTask task) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.surfaceCard,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.xl),
        ),
        title: Text(
          'Delete Task',
          style: AppTextStyles.h3.copyWith(
            color: AppColors.textPrimary,
          ),
        ),
        content: Text(
          'Are you sure you want to delete "${task.title}"? This action cannot be undone.',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              'Cancel',
              style: AppTextStyles.buttonMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              backgroundColor: AppColors.statusError.withValues(alpha: 0.1),
            ),
            child: Text(
              'Delete',
              style: AppTextStyles.buttonMedium.copyWith(
                color: AppColors.statusError,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    ) ?? false;
  }
}
