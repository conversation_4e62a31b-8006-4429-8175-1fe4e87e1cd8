import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:unstack/models/enhanced_task.dart';
import 'package:unstack/theme/app_theme.dart';
import 'package:unstack/widgets/glassmorphism_container.dart';

/// Enhanced task card with improved design and functionality
class EnhancedTaskCard extends StatefulWidget {
  final EnhancedTask task;
  final VoidCallback? onTap;
  final ValueChanged<bool>? onToggleComplete;
  final VoidCallback? onDelete;
  final VoidCallback? onEdit;
  final bool showProgress;
  final bool isCompact;
  final bool isDragging;

  const EnhancedTaskCard({
    super.key,
    required this.task,
    this.onTap,
    this.onToggleComplete,
    this.onDelete,
    this.onEdit,
    this.showProgress = true,
    this.isCompact = false,
    this.isDragging = false,
  });

  @override
  State<EnhancedTaskCard> createState() => _EnhancedTaskCardState();
}

class _EnhancedTaskCardState extends State<EnhancedTaskCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (!widget.isDragging) {
      _scaleController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    _scaleController.reverse();
  }

  void _handleTapCancel() {
    _scaleController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.isDragging ? 1.02 : _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _handleTapDown,
            onTapUp: _handleTapUp,
            onTapCancel: _handleTapCancel,
            onTap: widget.onTap,
            child: Container(
              margin: EdgeInsets.symmetric(
                horizontal: AppSpacing.md,
                vertical: AppSpacing.sm,
              ),
              child: GlassmorphismContainer(
                borderRadius: AppBorderRadius.xl,
                backgroundColor: widget.isDragging 
                    ? AppColors.glassBackground.withValues(alpha: 0.3)
                    : null,
                borderColor: widget.isDragging 
                    ? AppColors.accentPurple.withValues(alpha: 0.5)
                    : null,
                child: Padding(
                  padding: EdgeInsets.all(AppSpacing.lg),
                  child: Row(
                    children: [
                      // Completion checkbox
                      _buildCompletionCheckbox(),
                      
                      const SizedBox(width: AppSpacing.md),
                      
                      // Task content
                      Expanded(
                        child: _buildTaskContent(),
                      ),
                      
                      // Priority indicator and drag handle
                      _buildTrailingSection(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCompletionCheckbox() {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        widget.onToggleComplete?.call(!widget.task.isCompleted);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: widget.task.isCompleted
              ? AppColors.statusSuccess
              : Colors.transparent,
          border: Border.all(
            color: widget.task.isCompleted
                ? AppColors.statusSuccess
                : AppColors.textSecondary,
            width: 2,
          ),
        ),
        child: widget.task.isCompleted
            ? const Icon(
                Icons.check,
                color: Colors.white,
                size: 16,
              )
            : null,
      ),
    );
  }

  Widget _buildTaskContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Task title
        Text(
          widget.task.title,
          style: AppTextStyles.bodyLarge.copyWith(
            color: widget.task.isCompleted
                ? AppColors.textMuted
                : AppColors.textPrimary,
            decoration: widget.task.isCompleted
                ? TextDecoration.lineThrough
                : null,
            fontWeight: FontWeight.w600,
          ),
          maxLines: widget.isCompact ? 1 : 2,
          overflow: TextOverflow.ellipsis,
        ),
        
        // Task description
        if (widget.task.description.isNotEmpty && !widget.isCompact) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            widget.task.description,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
        
        // Tags
        if (widget.task.tags.isNotEmpty && !widget.isCompact) ...[
          const SizedBox(height: AppSpacing.sm),
          _buildTags(),
        ],
        
        // Due date
        if (widget.task.dueDate != null && !widget.isCompact) ...[
          const SizedBox(height: AppSpacing.xs),
          _buildDueDate(),
        ],
      ],
    );
  }

  Widget _buildTags() {
    return Wrap(
      spacing: AppSpacing.xs,
      runSpacing: AppSpacing.xs,
      children: widget.task.tags.take(3).map((tag) {
        return Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.sm,
            vertical: 2,
          ),
          decoration: BoxDecoration(
            color: AppColors.accentPurple.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(AppBorderRadius.sm),
          ),
          child: Text(
            tag,
            style: AppTextStyles.caption.copyWith(
              color: AppColors.accentPurple,
              fontWeight: FontWeight.w500,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildDueDate() {
    final dueDate = widget.task.dueDate!;
    final now = DateTime.now();
    final isOverdue = dueDate.isBefore(now) && !widget.task.isCompleted;
    final isDueToday = dueDate.year == now.year &&
        dueDate.month == now.month &&
        dueDate.day == now.day;

    Color dateColor = AppColors.textSecondary;
    if (isOverdue) {
      dateColor = AppColors.statusError;
    } else if (isDueToday) {
      dateColor = AppColors.statusWarning;
    }

    return Row(
      children: [
        Icon(
          Icons.schedule,
          size: 12,
          color: dateColor,
        ),
        const SizedBox(width: 4),
        Text(
          _formatDueDate(dueDate),
          style: AppTextStyles.caption.copyWith(
            color: dateColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildTrailingSection() {
    return Column(
      children: [
        // Priority indicator
        Container(
          width: 4,
          height: 40,
          decoration: BoxDecoration(
            color: widget.task.priority.color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        
        const SizedBox(height: AppSpacing.sm),
        
        // Drag handle
        Icon(
          Icons.drag_handle,
          color: AppColors.textMuted,
          size: 20,
        ),
      ],
    );
  }

  String _formatDueDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);

    if (dateOnly == today) {
      return 'Today';
    } else if (dateOnly == tomorrow) {
      return 'Tomorrow';
    } else if (dateOnly.isBefore(today)) {
      final difference = today.difference(dateOnly).inDays;
      return '$difference day${difference == 1 ? '' : 's'} overdue';
    } else {
      final difference = dateOnly.difference(today).inDays;
      return 'In $difference day${difference == 1 ? '' : 's'}';
    }
  }
}
