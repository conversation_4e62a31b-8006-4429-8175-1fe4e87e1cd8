import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:unstack/models/enhanced_streak_data.dart';
import 'package:unstack/theme/app_theme.dart';
import 'package:unstack/widgets/glassmorphism_container.dart';

class EnhancedStreakStatistics extends StatelessWidget {
  final StreakStatistics streakStatistics;
  final DateTime selectedMonth;

  const EnhancedStreakStatistics({
    super.key,
    required this.streakStatistics,
    required this.selectedMonth,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header Section
        GlassmorphismContainer(
          padding: const EdgeInsets.all(AppSpacing.lg),
          borderRadius: AppBorderRadius.xl,
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppSpacing.sm),
                decoration: BoxDecoration(
                  color: AppColors.accentPurple.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppBorderRadius.md),
                ),
                child: const Icon(
                  CupertinoIcons.chart_bar_alt_fill,
                  color: AppColors.accentPurple,
                  size: 20,
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Streak Statistics',
                      style: AppTextStyles.h3.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: AppSpacing.xs),
                    Text(
                      'Track your consistency and progress',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: AppSpacing.lg),

        // Main Statistics Grid
        Column(
          children: [
            // Current Streak (Featured)
            SizedBox(
              height: 180,
              child: _StreakCard(
                title: 'Current Streak',
                value: streakStatistics.currentStreak.toString(),
                subtitle: streakStatistics.currentStreak == 1 ? 'day' : 'days',
                icon: CupertinoIcons.flame_fill,
                color: AppColors.accentOrange,
                isLarge: true,
              ),
            ),
            const SizedBox(height: AppSpacing.md),

            // Side Statistics
            Row(
              children: [
                Expanded(
                  child: _StreakCard(
                    title: 'Longest',
                    value: streakStatistics.longestStreak.toString(),
                    subtitle:
                        streakStatistics.longestStreak == 1 ? 'day' : 'days',
                    icon: CupertinoIcons.star_fill,
                    color: AppColors.accentYellow,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: _StreakCard(
                    title: 'Total Days',
                    value: streakStatistics.totalCompletedDays.toString(),
                    subtitle: 'completed',
                    icon: CupertinoIcons.checkmark_circle_fill,
                    color: AppColors.accentGreen,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.md),

            // Additional Statistics
            Row(
              children: [
                Expanded(
                  child: _StreakCard(
                    title: 'Completion Rate',
                    value:
                        '${(streakStatistics.completionRate * 100).toStringAsFixed(0)}%',
                    subtitle: 'success rate',
                    icon: CupertinoIcons.percent,
                    color: AppColors.accentBlue,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: _StreakCard(
                    title: 'Avg Tasks',
                    value:
                        streakStatistics.averageTasksPerDay.toStringAsFixed(1),
                    subtitle: 'per day',
                    icon: CupertinoIcons.chart_pie_fill,
                    color: AppColors.accentPurple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }
}

class _StreakCard extends StatelessWidget {
  final String title;
  final String value;
  final String subtitle;
  final IconData icon;
  final Color color;
  final bool isLarge;

  const _StreakCard({
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    required this.color,
    this.isLarge = false,
  });

  @override
  Widget build(BuildContext context) {
    return GlassmorphismContainer(
      padding: EdgeInsets.all(isLarge ? AppSpacing.lg : AppSpacing.md),
      borderRadius: AppBorderRadius.lg,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: isLarge ? 24 : 20,
              ),
              const Spacer(),
              if (isLarge)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpacing.sm,
                    vertical: AppSpacing.xs,
                  ),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(AppBorderRadius.full),
                  ),
                  child: Text(
                    'ACTIVE',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: color,
                      fontWeight: FontWeight.w600,
                      fontSize: 10,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: isLarge ? AppSpacing.md : AppSpacing.sm),
          Text(
            value,
            style: (isLarge ? AppTextStyles.h1 : AppTextStyles.h2).copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(
            subtitle,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    )
        .animate()
        .fadeIn(delay: Duration(milliseconds: isLarge ? 200 : 400))
        .slideY(begin: 0.3, duration: 600.ms)
        .scale(begin: const Offset(0.9, 0.9), duration: 600.ms);
  }
}
