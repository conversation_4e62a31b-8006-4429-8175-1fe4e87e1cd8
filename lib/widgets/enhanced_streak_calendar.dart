import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:unstack/models/enhanced_streak_data.dart';
import 'package:unstack/theme/app_theme.dart';
import 'package:unstack/widgets/glassmorphism_container.dart';

class EnhancedStreakCalendar extends StatefulWidget {
  final DateTime selectedMonth;
  final Function(DateTime) onMonthChanged;
  final StreakStatistics streakStatistics;

  const EnhancedStreakCalendar({
    super.key,
    required this.selectedMonth,
    required this.onMonthChanged,
    required this.streakStatistics,
  });

  @override
  State<EnhancedStreakCalendar> createState() => _EnhancedStreakCalendarState();
}

class _EnhancedStreakCalendarState extends State<EnhancedStreakCalendar> {
  late PageController _pageController;
  late DateTime _currentMonth;

  @override
  void initState() {
    super.initState();
    _currentMonth = widget.selectedMonth;
    _pageController = PageController(
      initialPage: _getMonthIndex(_currentMonth),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  int _getMonthIndex(DateTime month) {
    final baseDate = DateTime(2020, 1, 1);
    return (month.year - baseDate.year) * 12 + (month.month - baseDate.month);
  }

  DateTime _getMonthFromIndex(int index) {
    final baseDate = DateTime(2020, 1, 1);
    final year = baseDate.year + (index ~/ 12);
    final month = baseDate.month + (index % 12);
    return DateTime(year, month, 1);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Month navigation
        _buildMonthNavigation(),
        const SizedBox(height: AppSpacing.lg),

        // Calendar grid
        _buildCalendarGrid(),
      ],
    );
  }

  Widget _buildMonthNavigation() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Previous month button
        GestureDetector(
          onTap: () => _changeMonth(-1),
          child: SizedBox(
            width: 40,
            height: 40,
            child: GlassmorphismContainer(
              borderRadius: AppBorderRadius.md,
              child: const Icon(
                CupertinoIcons.chevron_left,
                color: AppColors.textSecondary,
                size: 18,
              ),
            ),
          ),
        ),

        // Month and year
        Text(
          _getMonthYearString(_currentMonth),
          style: AppTextStyles.h3.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),

        // Next month button
        GestureDetector(
          onTap: () => _changeMonth(1),
          child: SizedBox(
            width: 40,
            height: 40,
            child: GlassmorphismContainer(
              borderRadius: AppBorderRadius.md,
              child: const Icon(
                CupertinoIcons.chevron_right,
                color: AppColors.textSecondary,
                size: 18,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCalendarGrid() {
    return Column(
      children: [
        // Weekday headers
        _buildWeekdayHeaders(),
        const SizedBox(height: AppSpacing.sm),

        // Calendar days
        _buildCalendarDays(),
      ],
    );
  }

  Widget _buildWeekdayHeaders() {
    const weekdays = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];

    return Row(
      children: weekdays.map((day) {
        return Expanded(
          child: Center(
            child: Text(
              day,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textMuted,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCalendarDays() {
    final days = _generateCalendarDays(_currentMonth);

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: 1,
        crossAxisSpacing: 6,
        mainAxisSpacing: 6,
      ),
      itemCount: days.length,
      itemBuilder: (context, index) {
        final date = days[index];
        final isCurrentMonth = _isCurrentMonth(date);
        final isToday = _isToday(date);
        final hasCompletedTasks = _hasCompletedTasksOnDate(date);

        return _CalendarDayWidget(
          date: date,
          isCurrentMonth: isCurrentMonth,
          isToday: isToday,
          hasCompletedTasks: hasCompletedTasks,
        )
            .animate(delay: Duration(milliseconds: 30 * index))
            .fadeIn(duration: 200.ms)
            .scale(begin: const Offset(0.9, 0.9), duration: 200.ms);
      },
    );
  }

  List<DateTime> _generateCalendarDays(DateTime month) {
    final firstDay = DateTime(month.year, month.month, 1);

    // Start from the first Sunday of the week containing the first day
    final startDate = firstDay.subtract(Duration(days: firstDay.weekday % 7));

    // Generate 42 days (6 weeks)
    return List.generate(42, (index) => startDate.add(Duration(days: index)));
  }

  bool _isCurrentMonth(DateTime date) {
    return date.month == _currentMonth.month && date.year == _currentMonth.year;
  }

  bool _isToday(DateTime date) {
    final today = DateTime.now();
    return date.year == today.year &&
        date.month == today.month &&
        date.day == today.day;
  }

  bool _hasCompletedTasksOnDate(DateTime date) {
    // For now, we'll use a simple heuristic based on the current streak
    // In a real implementation, this would check actual streak data for the date
    final today = DateTime.now();
    final daysDifference = today.difference(date).inDays;

    // Show flame icons for recent days within the current streak
    return daysDifference >= 0 &&
        daysDifference < widget.streakStatistics.currentStreak &&
        date.isBefore(today.add(const Duration(days: 1)));
  }

  void _changeMonth(int direction) {
    setState(() {
      _currentMonth =
          DateTime(_currentMonth.year, _currentMonth.month + direction, 1);
    });
    widget.onMonthChanged(_currentMonth);
  }

  String _getMonthYearString(DateTime date) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return '${months[date.month - 1]} ${date.year}';
  }
}

class _CalendarDayWidget extends StatelessWidget {
  final DateTime date;
  final bool isCurrentMonth;
  final bool isToday;
  final bool hasCompletedTasks;

  const _CalendarDayWidget({
    required this.date,
    required this.isCurrentMonth,
    required this.isToday,
    required this.hasCompletedTasks,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isToday
            ? AppColors.accentPurple.withValues(alpha: 0.15)
            : hasCompletedTasks
                ? AppColors.accentOrange.withValues(alpha: 0.1)
                : Colors.transparent,
        border: isToday
            ? Border.all(color: AppColors.accentPurple, width: 2)
            : hasCompletedTasks
                ? Border.all(
                    color: AppColors.accentOrange.withValues(alpha: 0.3),
                    width: 1)
                : null,
      ),
      child: Stack(
        children: [
          // Day number
          Center(
            child: Text(
              date.day.toString(),
              style: AppTextStyles.bodyMedium.copyWith(
                color: isCurrentMonth
                    ? (isToday ? AppColors.accentPurple : AppColors.textPrimary)
                    : AppColors.textMuted,
                fontWeight: isToday ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ),

          // Completion indicator (flame icon)
          if (hasCompletedTasks)
            Positioned(
              bottom: 4,
              right: 4,
              child: Icon(
                CupertinoIcons.flame_fill,
                color: AppColors.accentOrange,
                size: 10,
              ),
            ),
        ],
      ),
    );
  }
}
