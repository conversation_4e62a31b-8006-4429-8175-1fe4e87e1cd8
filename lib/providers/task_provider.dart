import 'package:flutter/foundation.dart';
import 'package:unstack/models/enhanced_task.dart';
import 'package:unstack/models/enhanced_streak_data.dart';
import 'package:unstack/services/streak_service.dart';
import 'package:unstack/utils/app_logger.dart';

/// Provider for managing task state and streak tracking
class TaskProvider extends ChangeNotifier {
  final StreakService _streakService;

  // Task state
  List<EnhancedTask> _allTasks = [];
  List<EnhancedTask> _todaysTasks = [];
  List<EnhancedTask> _tomorrowsTasks = [];
  List<EnhancedTask> _completedTasks = [];
  List<EnhancedTask> _pendingTasks = [];

  // Streak state
  StreakStatistics _streakStatistics = StreakStatistics.empty();
  bool _isTodaysStreakValid = false;

  // UI state
  bool _isLoading = false;
  String? _errorMessage;
  TaskSortOption _currentSortOption = TaskSortOption.displayOrder;
  bool _isAscending = true;
  TaskFilter _currentFilter = TaskFilter.all;

  TaskProvider({StreakService? streakService})
      : _streakService = streakService ?? StreakService() {
    _initialize();
  }

  // Getters
  List<EnhancedTask> get allTasks => List.unmodifiable(_allTasks);
  List<EnhancedTask> get todaysTasks => List.unmodifiable(_todaysTasks);
  List<EnhancedTask> get tomorrowsTasks => List.unmodifiable(_tomorrowsTasks);
  List<EnhancedTask> get completedTasks => List.unmodifiable(_completedTasks);
  List<EnhancedTask> get pendingTasks => List.unmodifiable(_pendingTasks);

  StreakStatistics get streakStatistics => _streakStatistics;
  bool get isTodaysStreakValid => _isTodaysStreakValid;

  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  TaskSortOption get currentSortOption => _currentSortOption;
  bool get isAscending => _isAscending;
  TaskFilter get currentFilter => _currentFilter;

  // Computed properties
  int get totalTasksCount => _allTasks.length;
  int get completedTasksCount =>
      _allTasks.where((task) => task.isCompleted).length;
  int get pendingTasksCount =>
      _allTasks.where((task) => !task.isCompleted).length;
  int get todaysTasksCount => _todaysTasks.length;
  int get todaysCompletedCount =>
      _todaysTasks.where((task) => task.isCompleted).length;

  double get todaysCompletionPercentage {
    if (_todaysTasks.isEmpty) return 0.0;
    return todaysCompletedCount / todaysTasksCount;
  }

  /// Initialize the provider
  Future<void> _initialize() async {
    try {
      _setLoading(true);
      await _streakService.initialize();
      _setupStreakListeners();
      await refreshTasks();
      _setLoading(false);
    } catch (e) {
      _setError('Failed to initialize task provider: $e');
      AppLogger.error('TaskProvider initialization failed: $e');
    }
  }

  /// Setup listeners for streak service streams
  void _setupStreakListeners() {
    _streakService.streakStatsStream.listen((stats) {
      _streakStatistics = stats;
      notifyListeners();
    });

    _streakService.todaysTasksStream.listen((tasks) {
      _todaysTasks = tasks;
      _updateFilteredTasks();
      notifyListeners();
    });

    _streakService.streakValidityStream.listen((isValid) {
      _isTodaysStreakValid = isValid;
      notifyListeners();
    });
  }

  /// Refresh all tasks from the database
  Future<void> refreshTasks() async {
    try {
      _setLoading(true);
      _clearError();

      // Get all tasks and categorize them
      _allTasks = await _streakService.taskRepository.getAllTasks();
      _todaysTasks = await _streakService.getTodaysTasks();
      _tomorrowsTasks = await _streakService.getTomorrowsTasks();
      _completedTasks = await _streakService.taskRepository.getCompletedTasks();
      _pendingTasks = await _streakService.taskRepository.getPendingTasks();

      _updateFilteredTasks();
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Failed to refresh tasks: $e');
      AppLogger.error('Failed to refresh tasks: $e');
    }
  }

  /// Add a new task
  Future<bool> addTask({
    required String title,
    required String description,
    required TaskPriority priority,
    required DateTime assignedDate,
    DateTime? dueDate,
    List<String> tags = const [],
  }) async {
    try {
      _clearError();

      final task = await _streakService.addTask(
        title: title,
        description: description,
        priority: priority,
        assignedDate: assignedDate,
        tags: tags,
      );

      // Update local state
      _allTasks.add(task);
      if (task.isAssignedToday) {
        _todaysTasks.add(task);
      } else if (task.isAssignedTomorrow) {
        _tomorrowsTasks.add(task);
      }
      _pendingTasks.add(task);

      _updateFilteredTasks();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to add task: $e');
      AppLogger.error('Failed to add task: $e');
      return false;
    }
  }

  /// Toggle task completion
  Future<bool> toggleTaskCompletion(String taskId, bool isCompleted) async {
    try {
      _clearError();

      final success =
          await _streakService.toggleTaskCompletion(taskId, isCompleted);
      if (success) {
        await refreshTasks();
      }
      return success;
    } catch (e) {
      _setError('Failed to toggle task completion: $e');
      AppLogger.error('Failed to toggle task completion: $e');
      return false;
    }
  }

  /// Delete a task
  Future<bool> deleteTask(String taskId) async {
    try {
      _clearError();

      final success = await _streakService.deleteTask(taskId);
      if (success) {
        await refreshTasks();
      }
      return success;
    } catch (e) {
      _setError('Failed to delete task: $e');
      AppLogger.error('Failed to delete task: $e');
      return false;
    }
  }

  /// Move task to a different date
  Future<bool> moveTaskToDate(String taskId, DateTime newDate) async {
    try {
      _clearError();

      final success = await _streakService.moveTaskToDate(taskId, newDate);
      if (success) {
        await refreshTasks();
      }
      return success;
    } catch (e) {
      _setError('Failed to move task: $e');
      AppLogger.error('Failed to move task: $e');
      return false;
    }
  }

  /// Reorder tasks
  Future<bool> reorderTasks(List<EnhancedTask> reorderedTasks) async {
    try {
      _clearError();

      final taskIds = reorderedTasks.map((task) => task.id).toList();
      final success = await _streakService.reorderTasks(taskIds);

      if (success) {
        // Update local state immediately for better UX
        _updateTasksOrder(reorderedTasks);
        notifyListeners();
      }

      return success;
    } catch (e) {
      _setError('Failed to reorder tasks: $e');
      AppLogger.error('Failed to reorder tasks: $e');
      return false;
    }
  }

  /// Update sorting option
  void updateSortOption(TaskSortOption sortOption, {bool? ascending}) {
    _currentSortOption = sortOption;
    if (ascending != null) {
      _isAscending = ascending;
    }
    _updateFilteredTasks();
    notifyListeners();
  }

  /// Update filter option
  void updateFilter(TaskFilter filter) {
    _currentFilter = filter;
    _updateFilteredTasks();
    notifyListeners();
  }

  /// Get filtered and sorted tasks
  List<EnhancedTask> getFilteredTasks() {
    List<EnhancedTask> tasks;

    switch (_currentFilter) {
      case TaskFilter.today:
        tasks = _todaysTasks;
        break;
      case TaskFilter.tomorrow:
        tasks = _tomorrowsTasks;
        break;
      case TaskFilter.completed:
        tasks = _completedTasks;
        break;
      case TaskFilter.pending:
        tasks = _pendingTasks;
        break;
      case TaskFilter.all:
        tasks = _allTasks;
        break;
    }

    return _sortTasks(List.from(tasks));
  }

  /// Sort tasks based on current sort option
  List<EnhancedTask> _sortTasks(List<EnhancedTask> tasks) {
    tasks.sort((a, b) {
      int comparison = 0;

      switch (_currentSortOption) {
        case TaskSortOption.dateCreated:
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case TaskSortOption.priority:
          comparison = a.priority.value.compareTo(b.priority.value);
          break;
        case TaskSortOption.alphabetical:
          comparison = a.title.toLowerCase().compareTo(b.title.toLowerCase());
          break;
        case TaskSortOption.dueDate:
          if (a.dueDate == null && b.dueDate == null) {
            comparison = 0;
          } else if (a.dueDate == null) {
            comparison = 1;
          } else if (b.dueDate == null) {
            comparison = -1;
          } else {
            comparison = a.dueDate!.compareTo(b.dueDate!);
          }
          break;
        case TaskSortOption.displayOrder:
          comparison = a.displayOrder.compareTo(b.displayOrder);
          break;
      }

      return _isAscending ? comparison : -comparison;
    });

    return tasks;
  }

  /// Update filtered tasks based on current filter and sort options
  void _updateFilteredTasks() {
    // This method is called to trigger UI updates when filter/sort changes
    // The actual filtering is done in getFilteredTasks()
  }

  /// Update tasks order in local state
  void _updateTasksOrder(List<EnhancedTask> reorderedTasks) {
    for (int i = 0; i < reorderedTasks.length; i++) {
      final taskIndex =
          _allTasks.indexWhere((task) => task.id == reorderedTasks[i].id);
      if (taskIndex != -1) {
        _allTasks[taskIndex] = reorderedTasks[i];
      }
    }
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    if (loading) _clearError();
    notifyListeners();
  }

  /// Set error message
  void _setError(String error) {
    _errorMessage = error;
    _isLoading = false;
    notifyListeners();
  }

  /// Clear error message
  void _clearError() {
    _errorMessage = null;
  }

  @override
  void dispose() {
    _streakService.dispose();
    super.dispose();
  }
}
