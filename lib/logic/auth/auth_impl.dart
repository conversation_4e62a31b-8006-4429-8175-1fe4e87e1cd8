import 'package:unstack/logic/auth/auth_contract.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:unstack/utils/app_logger.dart';
import 'package:google_sign_in/google_sign_in.dart';

class FirebaseAuthImpl implements IFirebaseAuthContract {
  @override
  Future<bool> signInWithEmail(String email) {
    // TODO: implement signInWithEmail
    throw UnimplementedError();
  }

  @override
  Future<bool> signInWithGoogle() {
    // TODO: implement signInWithGoogle
    throw UnimplementedError();
  }

  @override
  Future<bool> signOut() {
    // TODO: implement signOut
    throw UnimplementedError();
  }
}
