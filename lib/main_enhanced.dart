import 'package:device_preview/device_preview.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unstack/firebase_options.dart';
import 'package:unstack/models/enhanced_task.dart';
import 'package:unstack/providers/task_provider.dart';
import 'package:unstack/routes/route.dart';
import 'package:unstack/routes/route_utils.dart';
import 'package:unstack/theme/app_theme.dart';
import 'package:unstack/utils/app_logger.dart';
import 'package:unstack/views/enhanced_home_page.dart';
import 'package:unstack/widgets/enhanced_task_list.dart';
import 'package:unstack/widgets/glassmorphism_container.dart';
import 'package:unstack/widgets/home_app_bar_button.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    AppLogger.info('Firebase initialized successfully');
  } catch (e) {
    AppLogger.error('Firebase initialization failed: $e');
  }

  runApp(
    DevicePreview(
      enabled: kDebugMode,
      builder: (context) => const EnhancedUnstackApp(),
    ),
  );
}

class EnhancedUnstackApp extends StatelessWidget {
  const EnhancedUnstackApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) => TaskProvider(),
          lazy: false, // Initialize immediately
        ),
      ],
      child: MaterialApp(
        title: 'Unstack - Enhanced',
        debugShowCheckedModeBanner: false,
        debugShowMaterialGrid: false,
        theme: AppTheme.darkTheme,
        initialRoute: RoutePaths.onboardingScreen,
        onGenerateRoute: AppRouter.generateRoute,
        builder: DevicePreview.appBuilder,
      ),
    );
  }
}

/// Enhanced app router with new pages
class EnhancedAppRouter {
  static Route<dynamic>? generateRoute(RouteSettings settings) {
    // Use existing router but add enhanced pages
    switch (settings.name) {
      case RoutePaths.homePage:
        return _buildRoute(
          settings: settings,
          page: const PopScope(
            canPop: false,
            child: EnhancedHomePage(),
          ),
        );

      case RoutePaths.tasksListPage:
        return _buildRoute(
          settings: settings,
          page: const EnhancedTasksListPage(),
        );

      case RoutePaths.addTaskPage:
        final args = settings.arguments as Map<String, dynamic>?;
        return _buildRoute(
          settings: settings,
          page: EnhancedAddTaskPage(
            fromHomePage: args?['fromHomePage'] ?? false,
          ),
        );

      default:
        // Fall back to original router
        return AppRouter.generateRoute(settings);
    }
  }

  static PageRouteBuilder _buildRoute({
    required RouteSettings settings,
    required Widget page,
  }) {
    return PageRouteBuilder(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.easeInOut;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }
}

/// Enhanced tasks list page
class EnhancedTasksListPage extends StatefulWidget {
  const EnhancedTasksListPage({super.key});

  @override
  State<EnhancedTasksListPage> createState() => _EnhancedTasksListPageState();
}

class _EnhancedTasksListPageState extends State<EnhancedTasksListPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      body: SafeArea(
        child: Consumer<TaskProvider>(
          builder: (context, taskProvider, child) {
            return Column(
              children: [
                // App bar
                _buildAppBar(taskProvider),

                // Filter and sort controls
                _buildControls(taskProvider),

                // Task list
                Expanded(
                  child: _buildTaskList(taskProvider),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildAppBar(TaskProvider taskProvider) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Row(
        children: [
          // Back button
          HomeAppBarButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: CupertinoIcons.back,
          ),

          const SizedBox(width: AppSpacing.md),

          // Title
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Tasks',
                  style: AppTextStyles.h1.copyWith(
                    fontSize: 28,
                    fontWeight: FontWeight.w800,
                  ),
                ),
                Text(
                  '${taskProvider.pendingTasksCount} remaining, ${taskProvider.completedTasksCount} completed',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),

          // Add button
          HomeAppBarButton(
            onPressed: () {
              RouteUtils.pushNamed(context, RoutePaths.addTaskPage);
            },
            icon: CupertinoIcons.add,
          ),
        ],
      ),
    );
  }

  Widget _buildControls(TaskProvider taskProvider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.lg),
      child: Row(
        children: [
          // Filter dropdown
          Expanded(
            child: _buildFilterDropdown(taskProvider),
          ),

          const SizedBox(width: AppSpacing.md),

          // Sort button
          _buildSortButton(taskProvider),
        ],
      ),
    );
  }

  Widget _buildFilterDropdown(TaskProvider taskProvider) {
    return GlassmorphismContainer(
      borderRadius: AppBorderRadius.lg,
      child: DropdownButton<TaskFilter>(
        value: taskProvider.currentFilter,
        onChanged: (filter) {
          if (filter != null) {
            taskProvider.updateFilter(filter);
          }
        },
        items: TaskFilter.values.map((filter) {
          return DropdownMenuItem(
            value: filter,
            child: Text(filter.label),
          );
        }).toList(),
        underline: const SizedBox(),
        dropdownColor: AppColors.surfaceCard,
        style: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.textPrimary,
        ),
      ),
    );
  }

  Widget _buildSortButton(TaskProvider taskProvider) {
    return GestureDetector(
      onTap: () => _showSortOptions(taskProvider),
      child: GlassmorphismContainer(
        borderRadius: AppBorderRadius.lg,
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.md),
          child: Icon(
            CupertinoIcons.sort_down,
            color: AppColors.textPrimary,
          ),
        ),
      ),
    );
  }

  Widget _buildTaskList(TaskProvider taskProvider) {
    final tasks = taskProvider.getFilteredTasks();

    if (tasks.isEmpty) {
      return Center(
        child: Text(
          'No tasks found',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      );
    }

    return EnhancedTaskList(
      tasks: tasks,
      onTaskToggle: (task) {
        taskProvider.toggleTaskCompletion(task.id, task.isCompleted);
      },
      onTaskDelete: (task) {
        taskProvider.deleteTask(task.id);
      },
      onTasksReordered: (reorderedTasks) {
        taskProvider.reorderTasks(reorderedTasks);
      },
    );
  }

  void _showSortOptions(TaskProvider taskProvider) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(AppBorderRadius.xxl),
              topRight: Radius.circular(AppBorderRadius.xxl),
            ),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.glassBackground,
                AppColors.glassBackground.withValues(alpha: 0.05),
              ],
            ),
            border: Border.all(
              color: AppColors.glassBorder,
              width: 1,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: AppSpacing.md),
                decoration: BoxDecoration(
                  color: AppColors.textMuted,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Title
              Padding(
                padding: const EdgeInsets.all(AppSpacing.lg),
                child: Text(
                  'Sort Tasks',
                  style: AppTextStyles.h3.copyWith(
                    color: AppColors.textPrimary,
                  ),
                ),
              ),

              // Sort options
              ...TaskSortOption.values.map((option) {
                return ListTile(
                  title: Text(
                    option.label,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textPrimary,
                    ),
                  ),
                  trailing: taskProvider.currentSortOption == option
                      ? Icon(
                          Icons.check,
                          color: AppColors.accentPurple,
                        )
                      : null,
                  onTap: () {
                    taskProvider.updateSortOption(option);
                    Navigator.pop(context);
                  },
                );
              }),

              const SizedBox(height: AppSpacing.lg),
            ],
          ),
        );
      },
    );
  }
}

/// Enhanced add task page
class EnhancedAddTaskPage extends StatefulWidget {
  final bool fromHomePage;

  const EnhancedAddTaskPage({
    super.key,
    this.fromHomePage = false,
  });

  @override
  State<EnhancedAddTaskPage> createState() => _EnhancedAddTaskPageState();
}

class _EnhancedAddTaskPageState extends State<EnhancedAddTaskPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();

  TaskPriority _selectedPriority = TaskPriority.medium;
  DateTime _selectedDate = DateTime.now();
  final List<String> _tags = [];

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      body: SafeArea(
        child: Column(
          children: [
            // App bar
            _buildAppBar(),

            // Form
            Expanded(
              child: _buildForm(),
            ),

            // Add button
            _buildAddButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Row(
        children: [
          HomeAppBarButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: CupertinoIcons.back,
          ),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: Text(
              'Add Task',
              style: AppTextStyles.h1.copyWith(
                fontSize: 28,
                fontWeight: FontWeight.w800,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title field
            _buildTextField(
              controller: _titleController,
              label: 'Task Title',
              hint: 'Enter task title',
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a task title';
                }
                return null;
              },
            ),

            const SizedBox(height: AppSpacing.lg),

            // Description field
            _buildTextField(
              controller: _descriptionController,
              label: 'Description',
              hint: 'Enter task description',
              maxLines: 3,
            ),

            const SizedBox(height: AppSpacing.lg),

            // Priority selector
            _buildPrioritySelector(),

            const SizedBox(height: AppSpacing.lg),

            // Date selector
            _buildDateSelector(),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        GlassmorphismContainer(
          borderRadius: AppBorderRadius.lg,
          child: TextFormField(
            controller: controller,
            validator: validator,
            maxLines: maxLines,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textPrimary,
            ),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textMuted,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(AppSpacing.lg),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPrioritySelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Priority',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        Row(
          children: TaskPriority.values.map((priority) {
            final isSelected = _selectedPriority == priority;
            return Expanded(
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedPriority = priority;
                  });
                },
                child: Container(
                  margin: const EdgeInsets.only(right: AppSpacing.sm),
                  padding: const EdgeInsets.all(AppSpacing.md),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? priority.color.withValues(alpha: 0.2)
                        : AppColors.surfaceCard,
                    borderRadius: BorderRadius.circular(AppBorderRadius.lg),
                    border: Border.all(
                      color:
                          isSelected ? priority.color : AppColors.glassBorder,
                    ),
                  ),
                  child: Text(
                    priority.label,
                    style: AppTextStyles.bodySmall.copyWith(
                      color:
                          isSelected ? priority.color : AppColors.textSecondary,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildDateSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Assign to',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        Row(
          children: [
            Expanded(
              child: _buildDateOption('Today', DateTime.now()),
            ),
            const SizedBox(width: AppSpacing.sm),
            Expanded(
              child: _buildDateOption(
                  'Tomorrow', DateTime.now().add(const Duration(days: 1))),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateOption(String label, DateTime date) {
    final isSelected = _isSameDay(_selectedDate, date);

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedDate = date;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.lg),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.accentPurple.withValues(alpha: 0.2)
              : AppColors.surfaceCard,
          borderRadius: BorderRadius.circular(AppBorderRadius.lg),
          border: Border.all(
            color: isSelected ? AppColors.accentPurple : AppColors.glassBorder,
          ),
        ),
        child: Text(
          label,
          style: AppTextStyles.bodyMedium.copyWith(
            color:
                isSelected ? AppColors.accentPurple : AppColors.textSecondary,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildAddButton() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: SizedBox(
        width: double.infinity,
        child: Consumer<TaskProvider>(
          builder: (context, taskProvider, child) {
            return ElevatedButton(
              onPressed: taskProvider.isLoading ? null : _addTask,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.accentPurple,
                padding: const EdgeInsets.all(AppSpacing.lg),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppBorderRadius.lg),
                ),
              ),
              child: taskProvider.isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : Text(
                      'Add Task',
                      style: AppTextStyles.buttonLarge.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            );
          },
        ),
      ),
    );
  }

  Future<void> _addTask() async {
    if (_formKey.currentState!.validate()) {
      final taskProvider = context.read<TaskProvider>();

      final success = await taskProvider.addTask(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        priority: _selectedPriority,
        assignedDate: _selectedDate,
        tags: _tags,
      );

      if (success && mounted) {
        if (widget.fromHomePage) {
          RouteUtils.pushReplacementNamed(context, RoutePaths.tasksListPage);
        } else {
          Navigator.of(context).pop();
        }
      }
    }
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}
