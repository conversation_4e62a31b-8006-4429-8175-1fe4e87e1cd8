import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import 'package:unstack/providers/task_provider.dart';
import 'package:unstack/theme/app_theme.dart';
import 'package:unstack/widgets/enhanced_streak_calendar.dart';
import 'package:unstack/widgets/enhanced_streak_statistics.dart';

class EnhancedStreakPage extends StatefulWidget {
  const EnhancedStreakPage({super.key});

  @override
  State<EnhancedStreakPage> createState() => _EnhancedStreakPageState();
}

class _EnhancedStreakPageState extends State<EnhancedStreakPage> {
  DateTime _selectedMonth = DateTime.now();

  @override
  void initState() {
    super.initState();
    // Refresh streak data when page loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<TaskProvider>().refreshStreakData();
    });
  }

  void _onMonthChanged(DateTime month) {
    setState(() {
      _selectedMonth = month;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      body: SafeArea(
        child: Consumer<TaskProvider>(
          builder: (context, taskProvider, child) {
            if (taskProvider.isLoading) {
              return const Center(
                child: CircularProgressIndicator(
                  color: AppColors.accentPurple,
                ),
              );
            }

            if (taskProvider.errorMessage != null) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      CupertinoIcons.exclamationmark_triangle,
                      size: 64,
                      color: AppColors.textMuted,
                    ),
                    const SizedBox(height: AppSpacing.lg),
                    Text(
                      'Error loading streak data',
                      style: AppTextStyles.h3.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: AppSpacing.sm),
                    Text(
                      taskProvider.errorMessage!,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textMuted,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: AppSpacing.lg),
                    ElevatedButton(
                      onPressed: () => taskProvider.refreshStreakData(),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              );
            }

            return CustomScrollView(
              slivers: [
                // App bar
                SliverToBoxAdapter(
                  child: _buildAppBar(),
                ),

                // Calendar section
                SliverToBoxAdapter(
                  child: _buildCalendarSection(taskProvider),
                ),

                // Statistics section
                SliverToBoxAdapter(
                  child: _buildStatisticsSection(taskProvider),
                ),

                // Bottom spacing
                const SliverToBoxAdapter(
                  child: SizedBox(height: AppSpacing.xl),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Row(
        children: [
          // Back button
          Container(
            width: 44,
            height: 44,
            decoration: BoxDecoration(
              color: AppColors.surfaceCard,
              borderRadius: BorderRadius.circular(AppBorderRadius.md),
              border: Border.all(
                color: AppColors.glassBorder,
                width: 1,
              ),
            ),
            child: IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(
                CupertinoIcons.back,
                color: AppColors.textPrimary,
                size: 20,
              ),
            ),
          ),

          const SizedBox(width: AppSpacing.md),

          // Title
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Streak Tracking',
                  style: AppTextStyles.h1.copyWith(
                    fontSize: 28,
                    fontWeight: FontWeight.w800,
                  ),
                ),
                Text(
                  'Track your daily progress',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    )
        .animate()
        .slideY(
          begin: 0.3,
          duration: 400.ms,
          curve: Curves.easeOut,
        )
        .fadeIn();
  }

  Widget _buildCalendarSection(TaskProvider taskProvider) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppSpacing.lg),
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: AppColors.surfaceCard,
        borderRadius: BorderRadius.circular(AppBorderRadius.xl),
        border: Border.all(
          color: AppColors.glassBorder,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.accentOrange.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(AppBorderRadius.md),
                ),
                child: const Icon(
                  CupertinoIcons.calendar,
                  color: AppColors.accentOrange,
                  size: 20,
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Monthly Calendar',
                      style: AppTextStyles.h3.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: AppSpacing.xs),
                    Text(
                      'Track your daily task completion streaks',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSpacing.xl),
          EnhancedStreakCalendar(
            selectedMonth: _selectedMonth,
            onMonthChanged: _onMonthChanged,
            streakStatistics: taskProvider.streakStatistics,
          ),
        ],
      ),
    )
        .animate()
        .fadeIn(delay: 200.ms)
        .slideY(begin: 0.2, duration: 500.ms);
  }

  Widget _buildStatisticsSection(TaskProvider taskProvider) {
    return Container(
      margin: const EdgeInsets.all(AppSpacing.lg),
      child: EnhancedStreakStatistics(
        streakStatistics: taskProvider.streakStatistics,
        selectedMonth: _selectedMonth,
      ),
    )
        .animate()
        .fadeIn(delay: 300.ms)
        .slideY(begin: 0.2, duration: 500.ms);
  }
}
