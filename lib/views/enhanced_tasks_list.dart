import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import 'package:unstack/models/enhanced_task.dart';
import 'package:unstack/providers/task_provider.dart';
import 'package:unstack/routes/route.dart';
import 'package:unstack/theme/app_theme.dart';
import 'package:unstack/widgets/enhanced_task_card.dart';
import 'package:unstack/widgets/home_app_bar_button.dart';

class EnhancedTasksListPage extends StatefulWidget {
  const EnhancedTasksListPage({super.key});

  @override
  State<EnhancedTasksListPage> createState() => _EnhancedTasksListPageState();
}

class _EnhancedTasksListPageState extends State<EnhancedTasksListPage> {
  TaskSortOption _currentSortOption = TaskSortOption.displayOrder;
  bool _isAscending = true;

  @override
  void initState() {
    super.initState();
    // Refresh tasks when page loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<TaskProvider>().refreshTasks();
    });
  }

  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildSortBottomSheet(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      body: SafeArea(
        child: Column(
          children: [
            _buildAppBar(),
            Expanded(
              child: Consumer<TaskProvider>(
                builder: (context, taskProvider, child) {
                  if (taskProvider.isLoading) {
                    return const Center(
                      child: CircularProgressIndicator(
                        color: AppColors.accentPurple,
                      ),
                    );
                  }

                  if (taskProvider.errorMessage != null) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            CupertinoIcons.exclamationmark_triangle,
                            size: 64,
                            color: AppColors.textMuted,
                          ),
                          const SizedBox(height: AppSpacing.lg),
                          Text(
                            'Error loading tasks',
                            style: AppTextStyles.h3.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                          const SizedBox(height: AppSpacing.sm),
                          Text(
                            taskProvider.errorMessage!,
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.textMuted,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: AppSpacing.lg),
                          ElevatedButton(
                            onPressed: () => taskProvider.refreshTasks(),
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    );
                  }

                  final remainingTasks = taskProvider.pendingTasks;
                  final completedTasks = taskProvider.completedTasks;

                  return SingleChildScrollView(
                    child: Column(
                      children: [
                        // Remaining Tasks Section
                        ExpansionTile(
                          initiallyExpanded: true,
                          iconColor: AppColors.accentOrange,
                          shape: InputBorder.none,
                          title: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Remaining Tasks',
                                style: AppTextStyles.h3.copyWith(
                                  color: AppColors.textPrimary,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              IconButton(
                                onPressed: _showSortOptions,
                                icon: const Icon(CupertinoIcons.sort_down),
                                color: AppColors.textSecondary,
                              ),
                            ],
                          ),
                          childrenPadding: EdgeInsets.zero,
                          tilePadding: const EdgeInsets.symmetric(
                              horizontal: AppSpacing.lg),
                          children: [
                            SizedBox(
                              height: MediaQuery.of(context).size.height * 0.4,
                              child: _buildTaskList(
                                  remainingTasks, false, taskProvider),
                            ),
                          ],
                        ),

                        // Completed Tasks Section
                        ExpansionTile(
                          shape: InputBorder.none,
                          tilePadding: const EdgeInsets.symmetric(
                              horizontal: AppSpacing.lg),
                          iconColor: AppColors.accentOrange,
                          title: Text(
                            'Completed Tasks',
                            style: AppTextStyles.h3.copyWith(
                              color: AppColors.textPrimary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          children: [
                            SizedBox(
                              height: MediaQuery.of(context).size.height * 0.4,
                              child: _buildTaskList(
                                  completedTasks, true, taskProvider),
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Consumer<TaskProvider>(
      builder: (context, taskProvider, child) {
        final remainingCount = taskProvider.pendingTasks.length;
        final completedCount = taskProvider.completedTasks.length;

        return Padding(
          padding: const EdgeInsets.only(
            left: AppSpacing.md,
            top: AppSpacing.lg,
            right: AppSpacing.md,
            bottom: AppSpacing.sm,
          ),
          child: Row(
            children: [
              // Back button
              HomeAppBarButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: CupertinoIcons.back,
              ),

              const SizedBox(width: AppSpacing.md),

              // Title
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Tasks',
                      style: AppTextStyles.h1.copyWith(
                        fontSize: 28,
                        fontWeight: FontWeight.w800,
                      ),
                    ),
                    Text(
                      '$remainingCount remaining, $completedCount completed',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),

              // Add button
              HomeAppBarButton(
                onPressed: () {
                  RouteUtils.pushNamed(context, RoutePaths.addTaskPage);
                },
                icon: CupertinoIcons.add,
              ),
            ],
          ),
        )
            .animate()
            .slideY(
              begin: 0.3,
              duration: 400.ms,
              curve: Curves.easeOut,
            )
            .fadeIn();
      },
    );
  }

  Widget _buildTaskList(List<EnhancedTask> tasks, bool isCompletedList,
      TaskProvider taskProvider) {
    if (tasks.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isCompletedList
                  ? CupertinoIcons.check_mark_circled
                  : CupertinoIcons.circle,
              size: 64,
              color: AppColors.textMuted,
            ),
            const SizedBox(height: AppSpacing.lg),
            Text(
              isCompletedList ? 'No completed tasks yet' : 'No remaining tasks',
              style: AppTextStyles.h3.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              isCompletedList
                  ? 'Complete some tasks to see them here'
                  : 'All tasks completed! Great job!',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textMuted,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
      itemCount: tasks.length,
      itemBuilder: (context, index) {
        final task = tasks[index];
        return EnhancedTaskCard(
          key: ValueKey(task.id),
          task: task,
          onTap: () {
            // Navigate to task details if needed
          },
          onToggleComplete: (isCompleted) async {
            HapticFeedback.mediumImpact();
            await taskProvider.toggleTaskCompletion(task.id, isCompleted);
          },
        )
            .animate(delay: Duration(milliseconds: 50 * index))
            .slideY(
              begin: 0.3,
              duration: Duration(milliseconds: 300 + (index * 50)),
              curve: Curves.easeOut,
            )
            .fadeIn();
      },
    );
  }

  Widget _buildSortBottomSheet() {
    return Container(
      margin: const EdgeInsets.all(AppSpacing.lg),
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: AppColors.backgroundSecondary,
        borderRadius: BorderRadius.circular(AppBorderRadius.xl),
        border: Border.all(
          color: AppColors.glassBorder,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Sort Tasks',
            style: AppTextStyles.h3.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppSpacing.lg),
          ...TaskSortOption.values.map((option) {
            final isSelected = _currentSortOption == option;
            return ListTile(
              contentPadding: EdgeInsets.zero,
              leading: Icon(
                _getSortIcon(option),
                color: isSelected
                    ? AppColors.accentPurple
                    : AppColors.textSecondary,
                size: 20,
              ),
              title: Text(
                option.label,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: isSelected
                      ? AppColors.accentPurple
                      : AppColors.textPrimary,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
              trailing: isSelected
                  ? GestureDetector(
                      onTap: () {
                        setState(() {
                          _isAscending = !_isAscending;
                        });
                        Navigator.of(context).pop();
                      },
                      child: Icon(
                        _isAscending
                            ? CupertinoIcons.sort_up
                            : CupertinoIcons.sort_down,
                        color: AppColors.accentPurple,
                        size: 20,
                      ),
                    )
                  : null,
              onTap: () {
                setState(() {
                  _currentSortOption = option;
                });
                Navigator.of(context).pop();
              },
            );
          }),
          const SizedBox(height: AppSpacing.lg),
        ],
      ),
    )
        .animate()
        .slideY(
          begin: 0.3,
          duration: 300.ms,
          curve: Curves.easeOut,
        )
        .fadeIn();
  }

  IconData _getSortIcon(TaskSortOption option) {
    switch (option) {
      case TaskSortOption.dateCreated:
        return CupertinoIcons.calendar;
      case TaskSortOption.priority:
        return CupertinoIcons.flag;
      case TaskSortOption.alphabetical:
        return CupertinoIcons.textformat_abc;
      case TaskSortOption.dueDate:
        return CupertinoIcons.clock;
      case TaskSortOption.displayOrder:
        return CupertinoIcons.list_number;
    }
  }
}
