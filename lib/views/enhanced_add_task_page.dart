import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import 'package:unstack/models/enhanced_task.dart';
import 'package:unstack/providers/task_provider.dart';
import 'package:unstack/routes/route.dart';
import 'package:unstack/theme/app_theme.dart';
import 'package:unstack/widgets/glassmorphism_container.dart';
import 'package:unstack/widgets/home_app_bar_button.dart';

class EnhancedAddTaskPage extends StatefulWidget {
  final bool fromHomePage;
  final RouteSettings? routeSettings;

  const EnhancedAddTaskPage({
    super.key,
    this.fromHomePage = false,
    this.routeSettings,
  });

  @override
  State<EnhancedAddTaskPage> createState() => _EnhancedAddTaskPageState();
}

class _EnhancedAddTaskPageState extends State<EnhancedAddTaskPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();

  TaskPriority _selectedPriority = TaskPriority.medium;
  DateTime _selectedDate = DateTime.now();
  final List<String> _tags = [];

  @override
  void initState() {
    super.initState();
    // Check if fromHomePage is passed via route arguments
    if (widget.routeSettings?.arguments != null) {
      final args = widget.routeSettings!.arguments as Map<String, dynamic>;
      // Handle any route arguments if needed
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      body: SafeArea(
        child: Column(
          children: [
            // App bar
            _buildAppBar(),

            // Form
            Expanded(
              child: _buildForm(),
            ),

            // Add button
            _buildAddButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Row(
        children: [
          HomeAppBarButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: CupertinoIcons.back,
          ),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: Text(
              'Add Task',
              style: AppTextStyles.h1.copyWith(
                fontSize: 28,
                fontWeight: FontWeight.w800,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title field
            _buildTitleField(),
            const SizedBox(height: AppSpacing.xl),

            // Description field
            _buildDescriptionField(),
            const SizedBox(height: AppSpacing.xl),

            // Priority selection
            _buildPrioritySelection(),
            const SizedBox(height: AppSpacing.xl),

            // Date selection
            _buildDateSelection(),
            const SizedBox(height: AppSpacing.xl),
          ],
        ),
      ),
    );
  }

  Widget _buildTitleField() {
    return TextFormField(
      controller: _titleController,
      keyboardType: TextInputType.name,
      onChanged: (_) {
        setState(() {});
      },
      validator: (value) {
        if (value!.trim().length < 2) {
          return 'Please enter a valid task title';
        }
        return value.trim().isEmpty ? 'Please enter a valid task title' : null;
      },
      cursorColor: AppColors.whiteColor,
      style: AppTextStyles.bodyLarge.copyWith(
        color: AppColors.textPrimary,
        fontSize: 32,
        fontWeight: FontWeight.w600,
      ),
      decoration: InputDecoration(
        hintText: 'Task Title',
        hintStyle: AppTextStyles.bodyLarge.copyWith(
          color: AppColors.textMuted,
          fontSize: 32,
          fontWeight: FontWeight.w600,
        ),
        border: InputBorder.none,
        enabledBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
        errorBorder: InputBorder.none,
        focusedErrorBorder: InputBorder.none,
      ),
    );
  }

  Widget _buildDescriptionField() {
    return TextFormField(
      controller: _descriptionController,
      keyboardType: TextInputType.multiline,
      maxLines: 3,
      onChanged: (_) {
        setState(() {});
      },
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Please enter a task description';
        }
        return null;
      },
      cursorColor: AppColors.whiteColor,
      style: AppTextStyles.bodyMedium.copyWith(
        color: AppColors.textPrimary,
        fontSize: 18,
        fontWeight: FontWeight.w500,
      ),
      decoration: InputDecoration(
        hintText: 'Task Description',
        hintStyle: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.textMuted,
          fontSize: 18,
          fontWeight: FontWeight.w500,
        ),
        border: InputBorder.none,
        enabledBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
        errorBorder: InputBorder.none,
        focusedErrorBorder: InputBorder.none,
      ),
    );
  }

  Widget _buildPrioritySelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Priority',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        )
            .animate()
            .slideY(
              begin: 0.3,
              duration: 400.ms,
              curve: Curves.easeOut,
            )
            .fadeIn(),
        const SizedBox(height: AppSpacing.sm),
        Wrap(
          children: TaskPriority.values.map((priority) {
            final isSelected = _selectedPriority == priority;
            return GestureDetector(
              onTap: () => setState(() => _selectedPriority = priority),
              child: Container(
                margin: const EdgeInsets.only(
                  right: AppSpacing.md,
                  bottom: AppSpacing.md,
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpacing.md,
                  vertical: AppSpacing.sm,
                ),
                decoration: BoxDecoration(
                  color: isSelected
                      ? priority.color.withValues(alpha: 0.2)
                      : AppColors.backgroundTertiary,
                  borderRadius: BorderRadius.circular(AppBorderRadius.full),
                  border: Border.all(
                    color: isSelected
                        ? priority.color.withValues(alpha: 0.5)
                        : AppColors.backgroundTertiary,
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      CupertinoIcons.flag,
                      size: 18,
                      color: priority.color,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      priority.label,
                      style: AppTextStyles.caption.copyWith(
                        color: isSelected
                            ? priority.color
                            : AppColors.textSecondary,
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildDateSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Assigned Date',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        )
            .animate()
            .slideY(
              begin: 0.3,
              duration: 400.ms,
              curve: Curves.easeOut,
            )
            .fadeIn(),
        const SizedBox(height: AppSpacing.sm),
        Row(
          children: [
            _buildDateOption('Today', DateTime.now()),
            const SizedBox(width: AppSpacing.sm),
            _buildDateOption(
                'Tomorrow', DateTime.now().add(const Duration(days: 1))),
          ],
        ),
      ],
    );
  }

  Widget _buildDateOption(String label, DateTime date) {
    final isSelected = _isSameDay(_selectedDate, date);
    return Expanded(
      child: Padding(
        padding: EdgeInsets.only(right: label == 'Today' ? 8 : 0),
        child: ElevatedButton(
          onPressed: () => setState(() => _selectedDate = date),
          style: ElevatedButton.styleFrom(
            backgroundColor: isSelected
                ? AppColors.whiteColor
                : AppColors.backgroundTertiary,
            side: BorderSide(
              color: isSelected ? AppColors.textPrimary : AppColors.textMuted,
              width: 1,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppBorderRadius.full),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              isSelected
                  ? Icon(
                      CupertinoIcons.checkmark,
                      size: 16,
                      color: isSelected
                          ? AppColors.blackColor
                          : AppColors.textMuted,
                    )
                  : const SizedBox(),
              const SizedBox(width: 8),
              Text(
                label,
                style: AppTextStyles.bodyMedium.copyWith(
                  color:
                      isSelected ? AppColors.blackColor : AppColors.textMuted,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddButton() {
    final isValid = _titleController.text.trim().isNotEmpty;

    return Container(
      height: 80,
      margin: const EdgeInsets.all(AppSpacing.lg),
      width: double.infinity,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: isValid
              ? AppColors.whiteColor
              : AppColors.whiteColor.withAlpha(50),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(
              Radius.circular(48),
            ),
          ),
        ),
        onPressed: _addTask,
        child: Text(
          "Let's Complete this!",
          style: TextStyle(
            fontWeight: isValid ? FontWeight.w600 : FontWeight.w800,
            color: isValid ? AppColors.blackColor : AppColors.whiteColor,
          ),
        ),
      )
          .animate()
          .slideY(
            begin: 0.3,
            duration: 400.ms,
            curve: Curves.easeOut,
          )
          .fadeIn(),
    );
  }

  Future<void> _addTask() async {
    if (_formKey.currentState!.validate()) {
      final taskProvider = context.read<TaskProvider>();

      final success = await taskProvider.addTask(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        priority: _selectedPriority,
        assignedDate: _selectedDate,
        tags: _tags,
      );

      if (success && mounted) {
        if (widget.fromHomePage) {
          RouteUtils.pushReplacementNamed(context, RoutePaths.tasksListPage);
        } else {
          Navigator.of(context).pop();
        }
      }
    }
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}
