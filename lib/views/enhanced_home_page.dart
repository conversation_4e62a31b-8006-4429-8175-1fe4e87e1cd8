import 'dart:math';
import 'package:confetti/confetti.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unstack/models/enhanced_task.dart';
import 'package:unstack/providers/task_provider.dart';
import 'package:unstack/routes/route.dart';
import 'package:unstack/theme/app_theme.dart';
import 'package:unstack/widgets/enhanced_task_card.dart';
import 'package:unstack/widgets/home_app_bar_button.dart';
import 'package:unstack/widgets/streak_widget.dart';
import 'package:unstack/widgets/circular_progress_3d.dart';
import 'package:unstack/widgets/glassmorphism_container.dart';

class EnhancedHomePage extends StatefulWidget {
  const EnhancedHomePage({super.key});

  @override
  State<EnhancedHomePage> createState() => _EnhancedHomePageState();
}

class _EnhancedHomePageState extends State<EnhancedHomePage> {
  String _userName = '';
  late ConfettiController _confettiController;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _confettiController =
        ConfettiController(duration: const Duration(seconds: 2));
    _loadUserName();
    _initializeProvider();
  }

  @override
  void dispose() {
    _confettiController.dispose();
    super.dispose();
  }

  Future<void> _loadUserName() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final name = prefs.getString('user_name') ?? 'User';
      setState(() {
        _userName = name;
      });
    } catch (e) {
      setState(() {
        _userName = 'User';
      });
    }
  }

  Future<void> _initializeProvider() async {
    final taskProvider = context.read<TaskProvider>();
    await taskProvider.refreshTasks();
    setState(() {
      _isInitialized = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      body: SafeArea(
        child: Consumer<TaskProvider>(
          builder: (context, taskProvider, child) {
            if (!_isInitialized || taskProvider.isLoading) {
              return _buildLoadingState();
            }

            if (taskProvider.errorMessage != null) {
              return _buildErrorState(taskProvider.errorMessage!);
            }

            return _buildMainContent(taskProvider);
          },
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        color: AppColors.accentPurple,
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: GlassmorphismContainer(
        borderRadius: AppBorderRadius.xl,
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.xl),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.error_outline,
                color: AppColors.statusError,
                size: 48,
              ),
              const SizedBox(height: AppSpacing.md),
              Text(
                'Something went wrong',
                style: AppTextStyles.h3.copyWith(
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: AppSpacing.sm),
              Text(
                error,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppSpacing.lg),
              ElevatedButton(
                onPressed: () => context.read<TaskProvider>().refreshTasks(),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMainContent(TaskProvider taskProvider) {
    return Stack(
      children: [
        // Confetti overlay
        Align(
          alignment: Alignment.topCenter,
          child: ConfettiWidget(
            confettiController: _confettiController,
            blastDirection: pi / 2,
            maxBlastForce: 5,
            minBlastForce: 2,
            emissionFrequency: 0.05,
            numberOfParticles: 50,
            gravity: 0.05,
          ),
        ),

        // Main content
        CustomScrollView(
          slivers: [
            // App bar
            SliverToBoxAdapter(
              child: _buildAppBar(taskProvider),
            ),

            // Progress section
            SliverToBoxAdapter(
              child: _buildProgressSection(taskProvider),
            ),

            // Today's tasks section
            SliverToBoxAdapter(
              child: _buildTodaysTasksSection(taskProvider),
            ),

            // Task list
            SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final tasks = taskProvider.todaysTasks.take(5).toList();
                  if (index >= tasks.length) return null;

                  final task = tasks[index];
                  return EnhancedTaskCard(
                    task: task,
                    isCompact: true,
                    onTap: () => _navigateToTaskDetails(task),
                    onToggleComplete: (isCompleted) {
                      taskProvider.toggleTaskCompletion(task.id, isCompleted);
                      if (isCompleted) {
                        _checkForStreakCelebration(taskProvider);
                      }
                    },
                  );
                },
                childCount: taskProvider.todaysTasks.length > 5
                    ? 5
                    : taskProvider.todaysTasks.length,
              ),
            ),

            // View all tasks button
            if (taskProvider.todaysTasks.length > 5)
              SliverToBoxAdapter(
                child: _buildViewAllButton(),
              ),

            // Bottom spacing
            const SliverToBoxAdapter(
              child: SizedBox(height: AppSpacing.xxxl),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAppBar(TaskProvider taskProvider) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Row(
        children: [
          // Profile button
          HomeAppBarButton(
            onPressed: () =>
                RouteUtils.pushNamed(context, RoutePaths.profilePage),
            icon: CupertinoIcons.person_circle,
          ),

          const SizedBox(width: AppSpacing.md),

          // Title and greeting
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Hello, $_userName',
                  style: AppTextStyles.h1.copyWith(
                    fontSize: 28,
                    fontWeight: FontWeight.w800,
                  ),
                ),
                Text(
                  _getGreetingMessage(taskProvider),
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),

          // Streak widget
          StreakWidget(
            currentStreak: taskProvider.streakStatistics.currentStreak,
          ),

          const SizedBox(width: AppSpacing.md),

          // Add task button
          HomeAppBarButton(
            onPressed: () =>
                RouteUtils.pushNamed(context, RoutePaths.addTaskPage),
            icon: CupertinoIcons.add,
          ),
        ],
      ),
    )
        .animate()
        .slideY(
          begin: -0.3,
          duration: 400.ms,
          curve: Curves.easeOut,
        )
        .fadeIn();
  }

  Widget _buildProgressSection(TaskProvider taskProvider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.lg),
      child: GlassmorphismContainer(
        borderRadius: AppBorderRadius.xxl,
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.lg),
          child: Row(
            children: [
              // 3D Progress indicator
              CircularProgressIndicator3D(
                totalTasks: taskProvider.todaysTasksCount,
                completedTasks: taskProvider.todaysCompletedCount,
                size: 80,
                onTap: () => RouteUtils.pushNamed(
                    context, RoutePaths.progressAnalyticsPage),
              ),

              const SizedBox(width: AppSpacing.lg),

              // Progress details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Today\'s Progress',
                      style: AppTextStyles.h3.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: AppSpacing.sm),
                    Text(
                      '${taskProvider.todaysCompletedCount} of ${taskProvider.todaysTasksCount} tasks completed',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(height: AppSpacing.xs),
                    Text(
                      '${(taskProvider.todaysCompletionPercentage * 100).toInt()}% complete',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.accentGreen,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    )
        .animate()
        .slideY(
          begin: 0.3,
          duration: 500.ms,
          curve: Curves.easeOut,
        )
        .fadeIn();
  }

  Widget _buildTodaysTasksSection(TaskProvider taskProvider) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Today\'s Tasks',
            style: AppTextStyles.h2.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          if (taskProvider.todaysTasks.isNotEmpty)
            TextButton(
              onPressed: () =>
                  RouteUtils.pushNamed(context, RoutePaths.tasksListPage),
              child: Text(
                'View All',
                style: AppTextStyles.buttonMedium.copyWith(
                  color: AppColors.accentPurple,
                ),
              ),
            ),
        ],
      ),
    )
        .animate()
        .slideX(
          begin: 0.3,
          duration: 600.ms,
          curve: Curves.easeOut,
        )
        .fadeIn();
  }

  Widget _buildViewAllButton() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.lg),
      child: GestureDetector(
        onTap: () => RouteUtils.pushNamed(context, RoutePaths.tasksListPage),
        child: GlassmorphismContainer(
          borderRadius: AppBorderRadius.xl,
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.lg),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'View All Tasks',
                  style: AppTextStyles.buttonLarge.copyWith(
                    color: AppColors.accentPurple,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(width: AppSpacing.sm),
                Icon(
                  CupertinoIcons.arrow_right,
                  color: AppColors.accentPurple,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getGreetingMessage(TaskProvider taskProvider) {
    final hour = DateTime.now().hour;
    String timeGreeting;

    if (hour < 12) {
      timeGreeting = 'Good morning!';
    } else if (hour < 17) {
      timeGreeting = 'Good afternoon!';
    } else {
      timeGreeting = 'Good evening!';
    }

    if (taskProvider.todaysTasksCount == 0) {
      return '$timeGreeting No tasks for today.';
    } else if (taskProvider.todaysCompletedCount ==
        taskProvider.todaysTasksCount) {
      return '$timeGreeting All tasks completed! 🎉';
    } else {
      return '$timeGreeting ${taskProvider.pendingTasksCount} tasks remaining.';
    }
  }

  void _navigateToTaskDetails(EnhancedTask task) {
    // Navigate to task details page
    // Implementation depends on your routing setup
  }

  void _checkForStreakCelebration(TaskProvider taskProvider) {
    if (taskProvider.isTodaysStreakValid &&
        taskProvider.todaysCompletedCount == taskProvider.todaysTasksCount) {
      _confettiController.play();
      // Show streak celebration overlay if needed
    }
  }
}
